using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace CopyFile
{
    internal class LockedFile
    {
        public static byte[] ReadLockedFile(string fileName)
        {
            try
            {
                int pid = GetProcessIDByFileName(fileName)[0];
                IntPtr hfile = DuplicateHandleByFileName(pid, fileName);
                var oldFilePointer = Win32Api.SetFilePointer(hfile, 0, 0, 1);
                int size = Win32Api.SetFilePointer(hfile, 0, 0, 2);
                byte[] fileBuffer = new byte[size];
                IntPtr hProcess = Win32Api.OpenProcess(Win32Api.PROCESS_ACCESS_FLAGS.PROCESS_SUSPEND_RESUME, false, pid);
                Win32Api.NtSuspendProcess(hProcess);
                Win32Api.SetFilePointer(hfile, 0, 0, 0);
                Win32Api.ReadFile(hfile, fileBuffer, (uint)size, out _, IntPtr.Zero);
                Win32Api.SetFilePointer(hfile, oldFilePointer, 0, 0);
                Win32Api.CloseHandle(hfile);
                Win32Api.NtResumeProcess(hProcess);
                Win32Api.CloseHandle(hProcess);
                return fileBuffer;
            }
            catch { return null; }
        }

        public static List<Win32Api.SYSTEM_HANDLE_INFORMATION> GetHandles(int pid)
        {
            List<Win32Api.SYSTEM_HANDLE_INFORMATION> aHandles = new List<Win32Api.SYSTEM_HANDLE_INFORMATION>();
            int handle_info_size = Marshal.SizeOf(new Win32Api.SYSTEM_HANDLE_INFORMATION()) * 20000;
            IntPtr ptrHandleData = IntPtr.Zero;
            try
            {
                ptrHandleData = Marshal.AllocHGlobal(handle_info_size);
                int nLength = 0;

                while (Win32Api.NtQuerySystemInformation(Win32Api.CNST_SYSTEM_HANDLE_INFORMATION, ptrHandleData, handle_info_size, ref nLength) == Win32Api.STATUS_INFO_LENGTH_MISMATCH)
                {
                    handle_info_size = nLength;
                    Marshal.FreeHGlobal(ptrHandleData);
                    ptrHandleData = Marshal.AllocHGlobal(nLength);
                }
                if (IntPtr.Size == 8)
                {
                    int handle_count = Marshal.ReadIntPtr(ptrHandleData).ToInt32();
                    IntPtr ptrHandleItem = new IntPtr(ptrHandleData.ToInt64() + IntPtr.Size);

                    for (long lIndex = 0; lIndex < handle_count; lIndex++)
                    {
                        Win32Api.SYSTEM_HANDLE_INFORMATION oSystemHandleInfo = new Win32Api.SYSTEM_HANDLE_INFORMATION();
                        oSystemHandleInfo = (Win32Api.SYSTEM_HANDLE_INFORMATION)Marshal.PtrToStructure(ptrHandleItem, oSystemHandleInfo.GetType());
                        ptrHandleItem = new IntPtr(ptrHandleItem.ToInt64() + Marshal.SizeOf(oSystemHandleInfo.GetType()));
                        if (oSystemHandleInfo.ProcessID != pid) { continue; }
                        aHandles.Add(oSystemHandleInfo);
                    }
                }
                else
                {
                    int handle_count = Marshal.ReadIntPtr(ptrHandleData).ToInt32();
                    IntPtr ptrHandleItem = new IntPtr(ptrHandleData.ToInt32() + IntPtr.Size);

                    for (long lIndex = 0; lIndex < handle_count; lIndex++)
                    {
                        Win32Api.SYSTEM_HANDLE_INFORMATION oSystemHandleInfo = new Win32Api.SYSTEM_HANDLE_INFORMATION();
                        oSystemHandleInfo = (Win32Api.SYSTEM_HANDLE_INFORMATION)Marshal.PtrToStructure(ptrHandleItem, oSystemHandleInfo.GetType());
                        ptrHandleItem = new IntPtr(ptrHandleItem.ToInt32() + Marshal.SizeOf(new Win32Api.SYSTEM_HANDLE_INFORMATION()));
                        if (oSystemHandleInfo.ProcessID != pid) { continue; }
                        aHandles.Add(oSystemHandleInfo);
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                Marshal.FreeHGlobal(ptrHandleData);
            }
            return aHandles;
        }

        private static string TryGetName(IntPtr Handle)
        {
            Win32Api.IO_STATUS_BLOCK status = new Win32Api.IO_STATUS_BLOCK();
            uint bufferSize = 1024;
            var bufferPtr = Marshal.AllocHGlobal((int)bufferSize);
            Win32Api.NtQueryInformationFile(Handle, ref status, bufferPtr, bufferSize, Win32Api.FILE_INFORMATION_CLASS.FileNameInformation);
            var nameInfo = (Win32Api.FileNameInformation)Marshal.PtrToStructure(bufferPtr, typeof(Win32Api.FileNameInformation));
            if (nameInfo.NameLength > bufferSize || nameInfo.NameLength <= 0)
            {
                return null;
            }
            return Marshal.PtrToStringUni(new IntPtr((IntPtr.Size == 8 ? bufferPtr.ToInt64() : bufferPtr.ToInt32()) + 4), nameInfo.NameLength / 2);
        }

        public static IntPtr FindHandleByFileName(Win32Api.SYSTEM_HANDLE_INFORMATION systemHandleInformation, string filename, IntPtr processHandle)
        {
            IntPtr openProcessHandle = processHandle;
            try
            {
                if (!Win32Api.DuplicateHandle(openProcessHandle, new IntPtr(systemHandleInformation.Handle), Win32Api.GetCurrentProcess(), out var ipHandle, 0, false, Win32Api.DUPLICATE_SAME_ACCESS))
                {
                    return IntPtr.Zero;
                }
                int objectTypeInfoSize = 0x1000;
                IntPtr objectTypeInfo = Marshal.AllocHGlobal(objectTypeInfoSize);
                try
                {
                    int returnLength = 0;
                    if (Win32Api.NtQueryObject(ipHandle, (int)Win32Api.OBJECT_INFORMATION_CLASS.ObjectTypeInformation, objectTypeInfo, objectTypeInfoSize, ref returnLength) != 0)
                    {
                        return IntPtr.Zero;
                    }
                    var objectTypeInfoStruct = (Win32Api.OBJECT_TYPE_INFORMATION)Marshal.PtrToStructure(objectTypeInfo, typeof(Win32Api.OBJECT_TYPE_INFORMATION));
                    string typeName = objectTypeInfoStruct.Name.ToString();
                    if (typeName == "File")
                    {
                        string name = TryGetName(ipHandle);
                        if (name == filename.Substring(2, filename.Length - 2))
                            return ipHandle;
                    }
                }
                finally
                {
                    Marshal.FreeHGlobal(objectTypeInfo);
                }
            }
            catch { }

            return IntPtr.Zero;
        }

        public static string FindHandleWithFileName(Win32Api.SYSTEM_HANDLE_INFORMATION systemHandleInformation, string filename, IntPtr processHandle)
        {
            IntPtr openProcessHandle = processHandle;
            try
            {
                if (!Win32Api.DuplicateHandle(openProcessHandle, new IntPtr(systemHandleInformation.Handle), Win32Api.GetCurrentProcess(), out var ipHandle, 0, false, Win32Api.DUPLICATE_SAME_ACCESS))
                {
                    return "";
                }
                int objectTypeInfoSize = 0x1000;
                IntPtr objectTypeInfo = Marshal.AllocHGlobal(objectTypeInfoSize);
                try
                {
                    int returnLength = 0;
                    if (Win32Api.NtQueryObject(ipHandle, (int)Win32Api.OBJECT_INFORMATION_CLASS.ObjectTypeInformation, objectTypeInfo, objectTypeInfoSize, ref returnLength) != 0)
                    {
                        return "";
                    }
                    var objectTypeInfoStruct = (Win32Api.OBJECT_TYPE_INFORMATION)Marshal.PtrToStructure(objectTypeInfo, typeof(Win32Api.OBJECT_TYPE_INFORMATION));
                    string typeName = objectTypeInfoStruct.Name.ToString();
                    if (typeName == "File")
                    {
                        string name = TryGetName(ipHandle);
                        if (name.Contains(filename))
                            return name;
                    }
                }
                finally
                {
                    Marshal.FreeHGlobal(objectTypeInfo);
                }
            }
            catch { }

            return "";
        }

        private static IntPtr DuplicateHandleByFileName(int pid, string fileName)
        {
            IntPtr handle = IntPtr.Zero;
            List<Win32Api.SYSTEM_HANDLE_INFORMATION> syshInfos = GetHandles(pid);
            IntPtr processHandle = GetProcessHandle(pid);

            foreach (var t in syshInfos)
            {
                handle = FindHandleByFileName(t, fileName, processHandle);
                if (handle != IntPtr.Zero)
                {
                    Win32Api.CloseHandle(processHandle);
                    return handle;
                }
            }
            Win32Api.CloseHandle(processHandle);
            return handle;
        }

        private static List<int> GetProcessIDByFileName(string path)
        {
            List<int> result = new List<int>();
            var bufferPtr = IntPtr.Zero;
            var statusBlock = new Win32Api.IO_STATUS_BLOCK();

            try
            {
                var handle = GetFileHandle(path);
                uint bufferSize = 0x4000;
                bufferPtr = Marshal.AllocHGlobal((int)bufferSize);

                uint status;
                while ((status = Win32Api.NtQueryInformationFile(handle,
                    ref statusBlock, bufferPtr, bufferSize,
                    Win32Api.FILE_INFORMATION_CLASS.FileProcessIdsUsingFileInformation))
                    == Win32Api.STATUS_INFO_LENGTH_MISMATCH)
                {
                    Marshal.FreeHGlobal(bufferPtr);
                    bufferPtr = IntPtr.Zero;
                    bufferSize *= 2;
                    bufferPtr = Marshal.AllocHGlobal((int)bufferSize);
                }

                Win32Api.CloseHandle(handle);

                if (status != Win32Api.STATUS_SUCCESS)
                {
                    return result;
                }

                IntPtr readBuffer = bufferPtr;
                int numEntries = Marshal.ReadInt32(readBuffer); // NumberOfProcessIdsInList
                readBuffer = IntPtr.Size == 8 ? new IntPtr(readBuffer.ToInt64() + IntPtr.Size) : new IntPtr(readBuffer.ToInt32() + IntPtr.Size);
                for (int i = 0; i < numEntries; i++)
                {
                    IntPtr processId = Marshal.ReadIntPtr(readBuffer); // A single ProcessIdList[] element
                    result.Add(processId.ToInt32());
                    readBuffer = IntPtr.Size == 8 ? new IntPtr(readBuffer.ToInt64() + IntPtr.Size) : new IntPtr(readBuffer.ToInt32() + IntPtr.Size);
                }
            }
            catch { return result; }
            finally
            {
                if (bufferPtr != IntPtr.Zero)
                {
                    Marshal.FreeHGlobal(bufferPtr);
                }
            }
            return result;
        }

        private static IntPtr GetFileHandle(string name)
        {
            return Win32Api.CreateFile(name,
                0,
                FileShare.Read | FileShare.Write | FileShare.Delete,
                IntPtr.Zero,
                FileMode.Open,
                (int)FileAttributes.Normal,
                IntPtr.Zero);
        }

        public static IntPtr GetProcessHandle(int pid)
        {
            return Win32Api.OpenProcess(Win32Api.PROCESS_ACCESS_FLAGS.PROCESS_DUP_HANDLE | Win32Api.PROCESS_ACCESS_FLAGS.PROCESS_VM_READ, false, pid);
        }
    }
}
