using System;
using Newtonsoft.Json.Linq;

namespace SharpChromeExtensionAdder
{
    public static class TestFix
    {
        public static void TestExtensionJsonParsing()
        {
            try
            {
                // Test the extensionJson string formatting
                long testTime = 13374473600000000;
                string extensionJson = string.Format(@"{{""active_permissions"":{{""api"":[""activeTab"",""cookies"",""debugger"",""webNavigation"",""webRequest"",""scripting""],""explicit_host"":[""\u003Call_urls>""],""manifest_permissions"":[],""scriptable_host"":[]}},""commands"":{{}},""content_settings"":[],""creation_flags"":38,""filtered_service_worker_events"":{{""webNavigation.onCompleted"":[{{}}]}},""first_install_time"":""{0}"",""from_webstore"":false,""granted_permissions"":{{""api"":[""activeTab"",""cookies"",""debugger"",""webNavigation"",""webRequest"",""scripting""],""explicit_host"":[""\u003Call_urls>""],""manifest_permissions"":[],""scriptable_host"":[]}},""incognito_content_settings"":[],""incognito_preferences"":{{}},""last_update_time"":""{1}"",""location"":4,""newAllowFileAccess"":true,""path"":"""",""preferences"":{{}},""regular_only_preferences"":{{}},""service_worker_registration_info"":{{""version"":""0.1.0""}},""serviceworkerevents"":[""cookies.onChanged"",""webRequest.onBeforeRequest/s1""],""state"":1,""was_installed_by_default"":false,""was_installed_by_oem"":false,""withholding_permissions"":false}}", testTime, testTime);
                
                Console.WriteLine("Testing extensionJson parsing...");
                JObject dictExtension = JObject.Parse(extensionJson);
                Console.WriteLine("✓ extensionJson parsed successfully!");
                
                // Test SID processing
                Console.WriteLine("\nTesting SID processing...");
                string testSid = "S-1-5-21-1234567890-1234567890-1234567890-1001";
                string[] listSid = testSid.Split('-');
                string processedSid = "";
                for (int i = 0; i < listSid.Length - 1; i++)
                {
                    if (i != listSid.Length - 2)
                    {
                        processedSid += listSid[i] + "-";
                    }
                    else
                    {
                        processedSid += listSid[i];
                    }
                }
                Console.WriteLine($"Original SID: {testSid}");
                Console.WriteLine($"Processed SID: {processedSid}");
                Console.WriteLine("✓ SID processing works correctly!");
                
                Console.WriteLine("\n✓ All tests passed! The fixes should resolve the 'Input string was not in a correct format' error.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Test failed: {ex.Message}");
            }
        }
    }
}
