using Microsoft.Win32;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace BrowserLibrary
{
    public class FindBrowser
    {
        static Dictionary<string, string> BrowserPath = new Dictionary<string, string>
        {
            { "chrome.exe", @"Google\Chrome\Application"},
            //{ "browser.exe", @"Yandex\YandexBrowser\Application"},
            //{ "coccoc.exe", @"CocCoc\Browser\Application"},
            //{ "msedge.exe", @"Microsoft\Edge\Application"},
        };

        static string FindDirPath(string name)
        {
            try
            {
                // 遍历 Program Files 和 Program Files (x86) 文件夹
                foreach (var drive in DriveInfo.GetDrives())
                {
                    if (drive.DriveType == DriveType.Fixed)
                    {
                        foreach (string folder in new string[] { @"Program Files", @"Program Files (x86)" })
                        {
                            string path = Path.Combine(drive.Name, folder, BrowserPath[name], name);
                            if (File.Exists(path))
                            {
                                return path;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
            }

            return string.Empty;
        }

        static string FindRegeditPath(string name)
        {
            // 从注册表中查找 Chrome 的安装路径
            using (RegistryKey key = Registry.LocalMachine.OpenSubKey($@"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\{name}"))
            {
                if (key != null)
                {
                    var tmpBrowserPath = key.GetValue(null) as string;
                    if (File.Exists(tmpBrowserPath))
                        return tmpBrowserPath;
                }
            }
            return string.Empty;
        }

        static string FindDefullPath(string name)
        {
            string userLocal = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            var tmpBrowserPath = Path.Combine(userLocal, BrowserPath[name], name);
            if (File.Exists(tmpBrowserPath))
            {
                return tmpBrowserPath;
            }
            return string.Empty;
        }


        public static string FindBrowserPath(string name)
        {
            var tmpBrowserPath = FindDefullPath(name);
            if (!string.IsNullOrEmpty(tmpBrowserPath))
                return tmpBrowserPath;

            tmpBrowserPath = FindRegeditPath(name);
            if (!string.IsNullOrEmpty(tmpBrowserPath))
                return tmpBrowserPath;

            tmpBrowserPath = FindDirPath(name);
            if (!string.IsNullOrEmpty(tmpBrowserPath))
                return tmpBrowserPath;

            return string.Empty;
        }
    }
}
