using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;

namespace BrowserLibrary
{
    public static class SharpInjector
    {
        private static byte[] InjectAssembly64Bit = new byte[]//see "64bit inject c# run.asm"
        {
            0x55, 0x48, 0x89, 0xE5, 0x48, 0x83, 0xEC, 0x40, 0x49, 0x89, 0xCC, 0x41, 0xFF, 0x54, 0x24, 0x10, 0x48, 0x89, 0xC1, 0xBA, 0x08, 0x00, 0x00, 0x00, 0x41, 0xB8, 0x00, 0x04, 0x00, 0x00, 0x41, 0xFF, 0x14, 0x24, 0x48, 0x89, 0xC3, 0x49, 0x8B, 0x04, 0x24, 0x48, 0x89, 0x83, 0x90, 0x01, 0x00, 0x00, 0x49, 0x8B, 0x44, 0x24, 0x08, 0x48, 0x89, 0x83, 0x98, 0x01, 0x00, 0x00, 0x49, 0x8B, 0x44, 0x24, 0x10, 0x48, 0x89, 0x83, 0xA0, 0x01, 0x00, 0x00, 0x49, 0x8B, 0x44, 0x24, 0x18, 0x48, 0x89, 0x83, 0xA8, 0x01, 0x00, 0x00, 0x49, 0x8B, 0x44, 0x24, 0x20, 0x48, 0x89, 0x83, 0xB0, 0x01, 0x00, 0x00, 0x49, 0x8B, 0x44, 0x24, 0x28, 0x48, 0x89, 0x83, 0xD0, 0x00, 0x00, 0x00, 0x49, 0x8B, 0x44, 0x24, 0x30, 0x48, 0x89, 0x83, 0xD8, 0x00, 0x00, 0x00, 0x4C, 0x89, 0xE0, 0x48, 0x83, 0xC0, 0x40, 0x48, 0x89, 0x83, 0x58, 0x02, 0x00, 0x00, 0x4C, 0x89, 0xE0, 0x48, 0x83, 0xC0, 0x40, 0x49, 0x03, 0x44, 0x24, 0x38, 0x48, 0x83, 0xC0, 0x08, 0x48, 0x89, 0x83, 0x60, 0x02, 0x00, 0x00, 0xE8, 0x8B, 0x06, 0x00, 0x00, 0x48, 0x83, 0xF8, 0x00, 0x0F, 0x84, 0xEC, 0x03, 0x00, 0x00, 0x48, 0x89, 0x5B, 0x08, 0xE8, 0x41, 0x04, 0x00, 0x00, 0x48, 0x89, 0x43, 0x10, 0xE8, 0x81, 0x04, 0x00, 0x00, 0x48, 0x89, 0x43, 0x18, 0x48, 0x8B, 0x4B, 0x10, 0x48, 0x8B, 0x53, 0x18, 0x4C, 0x8B, 0x43, 0x08, 0xFF, 0x93, 0xF8, 0x01, 0x00, 0x00, 0x48, 0x83, 0xF8, 0x00, 0x0F, 0x85, 0xBA, 0x03, 0x00, 0x00, 0x48, 0x8B, 0x03, 0x48, 0x8B, 0x00, 0x48, 0x8B, 0x40, 0x28, 0x48, 0x89, 0x43, 0x20, 0x48, 0x89, 0xD8, 0x48, 0x83, 0xC0, 0x28, 0x48, 0x89, 0x43, 0x30, 0x48, 0x8B, 0x0B, 0x48, 0x8B, 0x53, 0x30, 0xFF, 0x53, 0x20, 0x48, 0x83, 0xF8, 0x00, 0x0F, 0x85, 0x8D, 0x03, 0x00, 0x00, 0xE8, 0x78, 0x04, 0x00, 0x00, 0x48, 0x89, 0x43, 0x38, 0x48, 0x89, 0xD8, 0x48, 0x83, 0xC0, 0x40, 0x48, 0x89, 0x43, 0x48, 0x48, 0x89, 0xD8, 0x48, 0x83, 0xC0, 0x50, 0x48, 0x89, 0x43, 0x58, 0x48, 0x8B, 0x43, 0x28, 0x48, 0x8B, 0x00, 0x48, 0x8B, 0x40, 0x18, 0x48, 0x89, 0x43, 0x60, 0x48, 0x8B, 0x4B, 0x28, 0x48, 0xC7, 0xC2, 0x01, 0x00, 0x00, 0x00, 0x4C, 0x8B, 0x43, 0x58, 0x49, 0xC7, 0xC1, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x53, 0x60, 0x48, 0x83, 0xF8, 0x00, 0x75, 0x1A, 0x48, 0x8B, 0x43, 0x50, 0x48, 0x8B, 0x00, 0x48, 0x8B, 0x00, 0x48, 0x8B, 0x4B, 0x50, 0x48, 0x8B, 0x53, 0x38, 0x4C, 0x8B, 0x43, 0x48, 0xFF, 0xD0, 0xEB, 0xC7, 0x48, 0x83, 0x7B, 0x40, 0x00, 0x0F, 0x84, 0x1B, 0x03, 0x00, 0x00, 0x48, 0x8B, 0x43, 0x40, 0x48, 0x8B, 0x00, 0x48, 0x8B, 0x40, 0x48, 0x48, 0x89, 0x43, 0x68, 0xE8, 0x40, 0x04, 0x00, 0x00, 0x48, 0x89, 0x43, 0x70, 0xE8, 0x80, 0x04, 0x00, 0x00, 0x48, 0x89, 0x43, 0x78, 0x48, 0x89, 0xD8, 0x48, 0x05, 0x80, 0x00, 0x00, 0x00, 0x48, 0x89, 0x83, 0x88, 0x00, 0x00, 0x00, 0x48, 0x8B, 0x4B, 0x40, 0x48, 0x8B, 0x53, 0x78, 0x4C, 0x8B, 0x43, 0x70, 0x4C, 0x8B, 0x8B, 0x88, 0x00, 0x00, 0x00, 0xFF, 0x53, 0x68, 0x48, 0x83, 0xF8, 0x00, 0x0F, 0x85, 0xCA, 0x02, 0x00, 0x00, 0x48, 0x83, 0xBB, 0x80, 0x00, 0x00, 0x00, 0x00, 0x0F, 0x84, 0xBC, 0x02, 0x00, 0x00, 0x48, 0x8B, 0x83, 0x80, 0x00, 0x00, 0x00, 0x48, 0x8B, 0x00, 0x48, 0x8B, 0x40, 0x50, 0x48, 0x89, 0x83, 0x90, 0x00, 0x00, 0x00, 0x48, 0x8B, 0x8B, 0x80, 0x00, 0x00, 0x00, 0xFF, 0x93, 0x90, 0x00, 0x00, 0x00, 0x48, 0x83, 0xF8, 0x00, 0x0F, 0x85, 0x90, 0x02, 0x00, 0x00, 0x48, 0x8B, 0x83, 0x80, 0x00, 0x00, 0x00, 0x48, 0x8B, 0x00, 0x48, 0x8B, 0x40, 0x68, 0x48, 0x89, 0x83, 0x98, 0x00, 0x00, 0x00, 0x48, 0x89, 0xD8, 0x48, 0x05, 0xA0, 0x00, 0x00, 0x00, 0x48, 0x89, 0x83, 0xA8, 0x00, 0x00, 0x00, 0xE8, 0x31, 0x04, 0x00, 0x00, 0x48, 0x89, 0x83, 0xB0, 0x00, 0x00, 0x00, 0x48, 0x8B, 0x8B, 0x80, 0x00, 0x00, 0x00, 0x48, 0x8B, 0x93, 0xA8, 0x00, 0x00, 0x00, 0xFF, 0x93, 0x98, 0x00, 0x00, 0x00, 0x48, 0x83, 0xBB, 0xA0, 0x00, 0x00, 0x00, 0x00, 0x0F, 0x84, 0x3D, 0x02, 0x00, 0x00, 0x48, 0x89, 0xD8, 0x48, 0x05, 0xB8, 0x00, 0x00, 0x00, 0x48, 0x89, 0x83, 0xC0, 0x00, 0x00, 0x00, 0x48, 0x8B, 0x83, 0xA0, 0x00, 0x00, 0x00, 0x48, 0x8B, 0x00, 0x48, 0x8B, 0x00, 0x48, 0x89, 0x83, 0xC8, 0x00, 0x00, 0x00, 0x48, 0x8B, 0x8B, 0xA0, 0x00, 0x00, 0x00, 0x48, 0x8B, 0x93, 0xB0, 0x00, 0x00, 0x00, 0x4C, 0x8B, 0x83, 0xC0, 0x00, 0x00, 0x00, 0xFF, 0x93, 0xC8, 0x00, 0x00, 0x00, 0x48, 0x83, 0xBB, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x0F, 0x84, 0xF0, 0x01, 0x00, 0x00, 0xB9, 0x08, 0x00, 0x00, 0x00, 0xE8, 0xF2, 0x01, 0x00, 0x00, 0x48, 0x89, 0x83, 0xE0, 0x00, 0x00, 0x00, 0x48, 0x8B, 0x93, 0xD8, 0x00, 0x00, 0x00, 0x89, 0x10, 0xC7, 0x40, 0x04, 0x00, 0x00, 0x00, 0x00, 0xB9, 0x11, 0x00, 0x00, 0x00, 0xBA, 0x01, 0x00, 0x00, 0x00, 0x4C, 0x8B, 0x83, 0xE0, 0x00, 0x00, 0x00, 0xFF, 0x93, 0xC8, 0x01, 0x00, 0x00, 0x48, 0x89, 0x83, 0xE8, 0x00, 0x00, 0x00, 0x48, 0x8B, 0x8B, 0xE8, 0x00, 0x00, 0x00, 0xFF, 0x93, 0xD0, 0x01, 0x00, 0x00, 0x48, 0x8B, 0x83, 0xE8, 0x00, 0x00, 0x00, 0x48, 0x8B, 0x48, 0x10, 0x48, 0x8B, 0x93, 0xD0, 0x00, 0x00, 0x00, 0x4C, 0x8B, 0x83, 0xD8, 0x00, 0x00, 0x00, 0xE8, 0x9A, 0x03, 0x00, 0x00, 0x48, 0x8B, 0x8B, 0xE8, 0x00, 0x00, 0x00, 0xFF, 0x93, 0xD8, 0x01, 0x00, 0x00, 0x48, 0x89, 0xD8, 0x48, 0x05, 0xF0, 0x00, 0x00, 0x00, 0x48, 0x89, 0x83, 0xF8, 0x00, 0x00, 0x00, 0x48, 0x8B, 0x83, 0xB8, 0x00, 0x00, 0x00, 0x48, 0x8B, 0x00, 0x48, 0x8B, 0x80, 0x68, 0x01, 0x00, 0x00, 0x48, 0x89, 0x83, 0x00, 0x01, 0x00, 0x00, 0x48, 0x8B, 0x8B, 0xB8, 0x00, 0x00, 0x00, 0x48, 0x8B, 0x93, 0xE8, 0x00, 0x00, 0x00, 0x4C, 0x8B, 0x83, 0xF8, 0x00, 0x00, 0x00, 0xFF, 0x93, 0x00, 0x01, 0x00, 0x00, 0x48, 0x83, 0xF8, 0x00, 0x48, 0x83, 0xBB, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x0F, 0x84, 0x24, 0x01, 0x00, 0x00, 0x48, 0x89, 0xD8, 0x48, 0x05, 0x08, 0x01, 0x00, 0x00, 0x48, 0x89, 0x83, 0x10, 0x01, 0x00, 0x00, 0xE8, 0x65, 0x03, 0x00, 0x00, 0x48, 0x89, 0x83, 0x18, 0x01, 0x00, 0x00, 0x48, 0x8B, 0x83, 0xF0, 0x00, 0x00, 0x00, 0x48, 0x8B, 0x00, 0x48, 0x8B, 0x80, 0x88, 0x00, 0x00, 0x00, 0x48, 0x89, 0x83, 0x20, 0x01, 0x00, 0x00, 0x48, 0x8B, 0x8B, 0xF0, 0x00, 0x00, 0x00, 0x48, 0x8B, 0x93, 0x18, 0x01, 0x00, 0x00, 0x4C, 0x8B, 0x83, 0x10, 0x01, 0x00, 0x00, 0xFF, 0x93, 0x20, 0x01, 0x00, 0x00, 0x48, 0x83, 0xF8, 0x00, 0x0F, 0x85, 0xCB, 0x00, 0x00, 0x00, 0x48, 0x83, 0xBB, 0x08, 0x01, 0x00, 0x00, 0x00, 0x0F, 0x84, 0xBD, 0x00, 0x00, 0x00, 0xB9, 0x0C, 0x00, 0x00, 0x00, 0xBA, 0x00, 0x00, 0x00, 0x00, 0x41, 0xB8, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x93, 0xE8, 0x01, 0x00, 0x00, 0x48, 0x89, 0x83, 0x28, 0x01, 0x00, 0x00, 0xE8, 0x0E, 0x03, 0x00, 0x00, 0x48, 0x89, 0x83, 0x30, 0x01, 0x00, 0x00, 0xE8, 0x02, 0x03, 0x00, 0x00, 0x48, 0x89, 0x83, 0x38, 0x01, 0x00, 0x00, 0x48, 0x8B, 0x83, 0x08, 0x01, 0x00, 0x00, 0x48, 0x8B, 0x00, 0x48, 0x8B, 0x80, 0xC8, 0x01, 0x00, 0x00, 0x48, 0x89, 0x83, 0x40, 0x01, 0x00, 0x00, 0xE8, 0xA4, 0x02, 0x00, 0x00, 0x48, 0x89, 0x83, 0x48, 0x01, 0x00, 0x00, 0x48, 0x8B, 0x8B, 0x08, 0x01, 0x00, 0x00, 0x48, 0x8B, 0x93, 0x48, 0x01, 0x00, 0x00, 0x41, 0xB8, 0x18, 0x01, 0x00, 0x00, 0x41, 0xB9, 0x00, 0x00, 0x00, 0x00, 0x48, 0x8B, 0x83, 0x30, 0x01, 0x00, 0x00, 0x48, 0x89, 0x44, 0x24, 0x20, 0x48, 0x8B, 0x83, 0x28, 0x01, 0x00, 0x00, 0x48, 0x89, 0x44, 0x24, 0x28, 0x48, 0x8B, 0xB3, 0x38, 0x01, 0x00, 0x00, 0x48, 0x8B, 0x06, 0x48, 0x89, 0x44, 0x24, 0x30, 0x48, 0x8B, 0x46, 0x08, 0x48, 0x89, 0x44, 0x24, 0x38, 0x48, 0x8B, 0x46, 0x10, 0x48, 0x89, 0x44, 0x24, 0x3E, 0xFF, 0x93, 0x40, 0x01, 0x00, 0x00, 0x48, 0x83, 0xF8, 0x00, 0x75, 0x05, 0x48, 0x89, 0xEC, 0x5D, 0xC3, 0x48, 0xC7, 0xC0, 0xFF, 0xFF, 0xFF, 0xFF, 0x48, 0x89, 0xEC, 0x5D, 0xC3, 0x55, 0x48, 0x89, 0xE5, 0x48, 0x83, 0xEC, 0x20, 0x48, 0x89, 0x4D, 0xF8, 0xFF, 0x93, 0xA0, 0x01, 0x00, 0x00, 0x48, 0x89, 0xC1, 0xBA, 0x08, 0x00, 0x00, 0x00, 0x4C, 0x8B, 0x45, 0xF8, 0xFF, 0x93, 0x90, 0x01, 0x00, 0x00, 0x48, 0x89, 0xEC, 0x5D, 0xC3, 0x55, 0x48, 0x89, 0xE5, 0x48, 0x83, 0xEC, 0x20, 0x48, 0x89, 0x4D, 0xF8, 0xFF, 0x93, 0xA0, 0x01, 0x00, 0x00, 0x48, 0x89, 0xC1, 0xBA, 0x00, 0x00, 0x00, 0x00, 0x4C, 0x8B, 0x45, 0xF8, 0xFF, 0x93, 0x98, 0x01, 0x00, 0x00, 0x48, 0x89, 0xEC, 0x5D, 0xC3, 0x55, 0x48, 0x89, 0xE5, 0x48, 0x83, 0xEC, 0x20, 0xB9, 0x10, 0x00, 0x00, 0x00, 0xE8, 0x9C, 0xFF, 0xFF, 0xFF, 0xC7, 0x00, 0x8D, 0x18, 0x80, 0x92, 0x66, 0xC7, 0x40, 0x04, 0x8E, 0x0E, 0x66, 0xC7, 0x40, 0x06, 0x67, 0x48, 0xC6, 0x40, 0x08, 0xB3, 0xC6, 0x40, 0x09, 0x0C, 0xC6, 0x40, 0x0A, 0x7F, 0xC6, 0x40, 0x0B, 0xA8, 0xC6, 0x40, 0x0C, 0x38, 0xC6, 0x40, 0x0D, 0x84, 0xC6, 0x40, 0x0E, 0xE8, 0xC6, 0x40, 0x0F, 0xDE, 0x48, 0x89, 0xEC, 0x5D, 0xC3, 0x55, 0x48, 0x89, 0xE5, 0x48, 0x83, 0xEC, 0x20, 0xB9, 0x10, 0x00, 0x00, 0x00, 0xE8, 0x53, 0xFF, 0xFF, 0xFF, 0xC7, 0x00, 0x9E, 0xDB, 0x32, 0xD3, 0x66, 0xC7, 0x40, 0x04, 0xB3, 0xB9, 0x66, 0xC7, 0x40, 0x06, 0x25, 0x41, 0xC6, 0x40, 0x08, 0x82, 0xC6, 0x40, 0x09, 0x07, 0xC6, 0x40, 0x0A, 0xA1, 0xC6, 0x40, 0x0B, 0x48, 0xC6, 0x40, 0x0C, 0x84, 0xC6, 0x40, 0x0D, 0xF5, 0xC6, 0x40, 0x0E, 0x32, 0xC6, 0x40, 0x0F, 0x16, 0x48, 0x89, 0xEC, 0x5D, 0xC3, 0x55, 0x48, 0x89, 0xE5, 0x48, 0x83, 0xEC, 0x20, 0xB9, 0x10, 0x00, 0x00, 0x00, 0xE8, 0x0A, 0xFF, 0xFF, 0xFF, 0xC7, 0x00, 0xD2, 0xD1, 0x39, 0xBD, 0x66, 0xC7, 0x40, 0x04, 0x2F, 0xBA, 0x66, 0xC7, 0x40, 0x06, 0x6A, 0x48, 0xC6, 0x40, 0x08, 0x89, 0xC6, 0x40, 0x09, 0xB0, 0xC6, 0x40, 0x0A, 0xB4, 0xC6, 0x40, 0x0B, 0xB0, 0xC6, 0x40, 0x0C, 0xCB, 0xC6, 0x40, 0x0D, 0x46, 0xC6, 0x40, 0x0E, 0x68, 0xC6, 0x40, 0x0F, 0x91, 0x48, 0x89, 0xEC, 0x5D, 0xC3, 0x55, 0x48, 0x89, 0xE5, 0x48, 0x83, 0xEC, 0x20, 0xB9, 0x10, 0x00, 0x00, 0x00, 0xE8, 0xC1, 0xFE, 0xFF, 0xFF, 0xC7, 0x00, 0x22, 0x67, 0x2F, 0xCB, 0x66, 0xC7, 0x40, 0x04, 0x3A, 0xAB, 0x66, 0xC7, 0x40, 0x06, 0xD2, 0x11, 0xC6, 0x40, 0x08, 0x9C, 0xC6, 0x40, 0x09, 0x40, 0xC6, 0x40, 0x0A, 0x00, 0xC6, 0x40, 0x0B, 0xC0, 0xC6, 0x40, 0x0C, 0x4F, 0xC6, 0x40, 0x0D, 0xA3, 0xC6, 0x40, 0x0E, 0x0A, 0xC6, 0x40, 0x0F, 0x3E, 0x48, 0x89, 0xEC, 0x5D, 0xC3, 0x55, 0x48, 0x89, 0xE5, 0x48, 0x83, 0xEC, 0x20, 0xB9, 0x10, 0x00, 0x00, 0x00, 0xE8, 0x78, 0xFE, 0xFF, 0xFF, 0xC7, 0x00, 0x23, 0x67, 0x2F, 0xCB, 0x66, 0xC7, 0x40, 0x04, 0x3A, 0xAB, 0x66, 0xC7, 0x40, 0x06, 0xD2, 0x11, 0xC6, 0x40, 0x08, 0x9C, 0xC6, 0x40, 0x09, 0x40, 0xC6, 0x40, 0x0A, 0x00, 0xC6, 0x40, 0x0B, 0xC0, 0xC6, 0x40, 0x0C, 0x4F, 0xC6, 0x40, 0x0D, 0xA3, 0xC6, 0x40, 0x0E, 0x0A, 0xC6, 0x40, 0x0F, 0x3E, 0x48, 0x89, 0xEC, 0x5D, 0xC3, 0x55, 0x48, 0x89, 0xE5, 0x48, 0x83, 0xEC, 0x20, 0xB9, 0x10, 0x00, 0x00, 0x00, 0xE8, 0x2F, 0xFE, 0xFF, 0xFF, 0xC7, 0x00, 0xDC, 0x96, 0xF6, 0x05, 0x66, 0xC7, 0x40, 0x04, 0x29, 0x2B, 0x66, 0xC7, 0x40, 0x06, 0x63, 0x36, 0xC6, 0x40, 0x08, 0xAD, 0xC6, 0x40, 0x09, 0x8B, 0xC6, 0x40, 0x0A, 0xC4, 0xC6, 0x40, 0x0B, 0x38, 0xC6, 0x40, 0x0C, 0x9C, 0xC6, 0x40, 0x0D, 0xF2, 0xC6, 0x40, 0x0E, 0xA7, 0xC6, 0x40, 0x0F, 0x13, 0x48, 0x89, 0xEC, 0x5D, 0xC3, 0x55, 0x48, 0x89, 0xE5, 0x48, 0x83, 0xEC, 0x20, 0x8A, 0x02, 0x88, 0x01, 0x48, 0xFF, 0xC2, 0x48, 0xFF, 0xC1, 0x49, 0xFF, 0xC8, 0x49, 0x83, 0xF8, 0x00, 0x75, 0xED, 0xB8, 0x01, 0x00, 0x00, 0x00, 0x48, 0x89, 0xEC, 0x5D, 0xC3, 0x55, 0x48, 0x89, 0xE5, 0x48, 0x83, 0xEC, 0x20, 0x48, 0x8B, 0x83, 0x60, 0x02, 0x00, 0x00, 0x48, 0x89, 0xC1, 0xFF, 0x93, 0xE0, 0x01, 0x00, 0x00, 0x48, 0x89, 0xEC, 0x5D, 0xC3, 0x55, 0x48, 0x89, 0xE5, 0x48, 0x83, 0xEC, 0x20, 0x48, 0x8B, 0x83, 0x58, 0x02, 0x00, 0x00, 0x48, 0x89, 0xC1, 0xFF, 0x93, 0xE0, 0x01, 0x00, 0x00, 0x48, 0x89, 0xEC, 0x5D, 0xC3, 0x55, 0x48, 0x89, 0xE5, 0x48, 0x83, 0xEC, 0x20, 0xB9, 0x18, 0x00, 0x00, 0x00, 0xE8, 0x87, 0xFD, 0xFF, 0xFF, 0x50, 0x48, 0x89, 0xC1, 0xFF, 0x93, 0xF0, 0x01, 0x00, 0x00, 0x58, 0x48, 0x89, 0xEC, 0x5D, 0xC3, 0x55, 0x48, 0x89, 0xE5, 0x48, 0x83, 0xEC, 0x28, 0x41, 0x54, 0xB9, 0x0C, 0x00, 0x00, 0x00, 0xE8, 0x63, 0xFD, 0xFF, 0xFF, 0xC6, 0x00, 0x6D, 0xC6, 0x40, 0x01, 0x73, 0xC6, 0x40, 0x02, 0x63, 0xC6, 0x40, 0x03, 0x6F, 0xC6, 0x40, 0x04, 0x72, 0xC6, 0x40, 0x05, 0x65, 0xC6, 0x40, 0x06, 0x65, 0xC6, 0x40, 0x07, 0x2E, 0xC6, 0x40, 0x08, 0x64, 0xC6, 0x40, 0x09, 0x6C, 0xC6, 0x40, 0x0A, 0x6C, 0xC6, 0x40, 0x0B, 0x00, 0x49, 0x89, 0xC4, 0x48, 0x89, 0xC1, 0xFF, 0x93, 0xA8, 0x01, 0x00, 0x00, 0x48, 0x83, 0xF8, 0x00, 0x0F, 0x84, 0xB0, 0x03, 0x00, 0x00, 0x48, 0x89, 0x83, 0xB8, 0x01, 0x00, 0x00, 0x4C, 0x89, 0xE1, 0xE8, 0x38, 0xFD, 0xFF, 0xFF, 0xB9, 0x0D, 0x00, 0x00, 0x00, 0xE8, 0x05, 0xFD, 0xFF, 0xFF, 0xC6, 0x00, 0x6F, 0xC6, 0x40, 0x01, 0x6C, 0xC6, 0x40, 0x02, 0x65, 0xC6, 0x40, 0x03, 0x61, 0xC6, 0x40, 0x04, 0x75, 0xC6, 0x40, 0x05, 0x74, 0xC6, 0x40, 0x06, 0x33, 0xC6, 0x40, 0x07, 0x32, 0xC6, 0x40, 0x08, 0x2E, 0xC6, 0x40, 0x09, 0x64, 0xC6, 0x40, 0x0A, 0x6C, 0xC6, 0x40, 0x0B, 0x6C, 0xC6, 0x40, 0x0C, 0x00, 0x49, 0x89, 0xC4, 0x48, 0x89, 0xC1, 0xFF, 0x93, 0xA8, 0x01, 0x00, 0x00, 0x48, 0x83, 0xF8, 0x00, 0x0F, 0x84, 0x4E, 0x03, 0x00, 0x00, 0x48, 0x89, 0x83, 0xC0, 0x01, 0x00, 0x00, 0x4C, 0x89, 0xE1, 0xE8, 0xD6, 0xFC, 0xFF, 0xFF, 0xB9, 0x10, 0x00, 0x00, 0x00, 0xE8, 0xA3, 0xFC, 0xFF, 0xFF, 0xC6, 0x00, 0x53, 0xC6, 0x40, 0x01, 0x61, 0xC6, 0x40, 0x02, 0x66, 0xC6, 0x40, 0x03, 0x65, 0xC6, 0x40, 0x04, 0x41, 0xC6, 0x40, 0x05, 0x72, 0xC6, 0x40, 0x06, 0x72, 0xC6, 0x40, 0x07, 0x61, 0xC6, 0x40, 0x08, 0x79, 0xC6, 0x40, 0x09, 0x43, 0xC6, 0x40, 0x0A, 0x72, 0xC6, 0x40, 0x0B, 0x65, 0xC6, 0x40, 0x0C, 0x61, 0xC6, 0x40, 0x0D, 0x74, 0xC6, 0x40, 0x0E, 0x65, 0xC6, 0x40, 0x0F, 0x00, 0x49, 0x89, 0xC4, 0x48, 0x8B, 0x8B, 0xC0, 0x01, 0x00, 0x00, 0x48, 0x89, 0xC2, 0xFF, 0x93, 0xB0, 0x01, 0x00, 0x00, 0x48, 0x83, 0xF8, 0x00, 0x0F, 0x84, 0xD9, 0x02, 0x00, 0x00, 0x48, 0x89, 0x83, 0xC8, 0x01, 0x00, 0x00, 0x4C, 0x89, 0xE1, 0xE8, 0x61, 0xFC, 0xFF, 0xFF, 0xB9, 0x0E, 0x00, 0x00, 0x00, 0xE8, 0x2E, 0xFC, 0xFF, 0xFF, 0xC6, 0x00, 0x53, 0xC6, 0x40, 0x01, 0x61, 0xC6, 0x40, 0x02, 0x66, 0xC6, 0x40, 0x03, 0x65, 0xC6, 0x40, 0x04, 0x41, 0xC6, 0x40, 0x05, 0x72, 0xC6, 0x40, 0x06, 0x72, 0xC6, 0x40, 0x07, 0x61, 0xC6, 0x40, 0x08, 0x79, 0xC6, 0x40, 0x09, 0x4C, 0xC6, 0x40, 0x0A, 0x6F, 0xC6, 0x40, 0x0B, 0x63, 0xC6, 0x40, 0x0C, 0x6B, 0xC6, 0x40, 0x0D, 0x00, 0x49, 0x89, 0xC4, 0x48, 0x8B, 0x8B, 0xC0, 0x01, 0x00, 0x00, 0x48, 0x89, 0xC2, 0xFF, 0x93, 0xB0, 0x01, 0x00, 0x00, 0x48, 0x83, 0xF8, 0x00, 0x0F, 0x84, 0x6C, 0x02, 0x00, 0x00, 0x48, 0x89, 0x83, 0xD0, 0x01, 0x00, 0x00, 0x4C, 0x89, 0xE1, 0xE8, 0xF4, 0xFB, 0xFF, 0xFF, 0xB9, 0x10, 0x00, 0x00, 0x00, 0xE8, 0xC1, 0xFB, 0xFF, 0xFF, 0xC6, 0x00, 0x53, 0xC6, 0x40, 0x01, 0x61, 0xC6, 0x40, 0x02, 0x66, 0xC6, 0x40, 0x03, 0x65, 0xC6, 0x40, 0x04, 0x41, 0xC6, 0x40, 0x05, 0x72, 0xC6, 0x40, 0x06, 0x72, 0xC6, 0x40, 0x07, 0x61, 0xC6, 0x40, 0x08, 0x79, 0xC6, 0x40, 0x09, 0x55, 0xC6, 0x40, 0x0A, 0x6E, 0xC6, 0x40, 0x0B, 0x6C, 0xC6, 0x40, 0x0C, 0x6F, 0xC6, 0x40, 0x0D, 0x63, 0xC6, 0x40, 0x0E, 0x6B, 0xC6, 0x40, 0x0F, 0x00, 0x49, 0x89, 0xC4, 0x48, 0x8B, 0x8B, 0xC0, 0x01, 0x00, 0x00, 0x48, 0x89, 0xC2, 0xFF, 0x93, 0xB0, 0x01, 0x00, 0x00, 0x48, 0x83, 0xF8, 0x00, 0x0F, 0x84, 0xF7, 0x01, 0x00, 0x00, 0x48, 0x89, 0x83, 0xD8, 0x01, 0x00, 0x00, 0x4C, 0x89, 0xE1, 0xE8, 0x7F, 0xFB, 0xFF, 0xFF, 0xB9, 0x0F, 0x00, 0x00, 0x00, 0xE8, 0x4C, 0xFB, 0xFF, 0xFF, 0xC6, 0x00, 0x53, 0xC6, 0x40, 0x01, 0x79, 0xC6, 0x40, 0x02, 0x73, 0xC6, 0x40, 0x03, 0x41, 0xC6, 0x40, 0x04, 0x6C, 0xC6, 0x40, 0x05, 0x6C, 0xC6, 0x40, 0x06, 0x6F, 0xC6, 0x40, 0x07, 0x63, 0xC6, 0x40, 0x08, 0x53, 0xC6, 0x40, 0x09, 0x74, 0xC6, 0x40, 0x0A, 0x72, 0xC6, 0x40, 0x0B, 0x69, 0xC6, 0x40, 0x0C, 0x6E, 0xC6, 0x40, 0x0D, 0x67, 0xC6, 0x40, 0x0E, 0x00, 0x49, 0x89, 0xC4, 0x48, 0x8B, 0x8B, 0xC0, 0x01, 0x00, 0x00, 0x48, 0x89, 0xC2, 0xFF, 0x93, 0xB0, 0x01, 0x00, 0x00, 0x48, 0x83, 0xF8, 0x00, 0x0F, 0x84, 0x86, 0x01, 0x00, 0x00, 0x48, 0x89, 0x83, 0xE0, 0x01, 0x00, 0x00, 0x4C, 0x89, 0xE1, 0xE8, 0x0E, 0xFB, 0xFF, 0xFF, 0xB9, 0x16, 0x00, 0x00, 0x00, 0xE8, 0xDB, 0xFA, 0xFF, 0xFF, 0xC6, 0x00, 0x53, 0xC6, 0x40, 0x01, 0x61, 0xC6, 0x40, 0x02, 0x66, 0xC6, 0x40, 0x03, 0x65, 0xC6, 0x40, 0x04, 0x41, 0xC6, 0x40, 0x05, 0x72, 0xC6, 0x40, 0x06, 0x72, 0xC6, 0x40, 0x07, 0x61, 0xC6, 0x40, 0x08, 0x79, 0xC6, 0x40, 0x09, 0x43, 0xC6, 0x40, 0x0A, 0x72, 0xC6, 0x40, 0x0B, 0x65, 0xC6, 0x40, 0x0C, 0x61, 0xC6, 0x40, 0x0D, 0x74, 0xC6, 0x40, 0x0E, 0x65, 0xC6, 0x40, 0x0F, 0x56, 0xC6, 0x40, 0x10, 0x65, 0xC6, 0x40, 0x11, 0x63, 0xC6, 0x40, 0x12, 0x74, 0xC6, 0x40, 0x13, 0x6F, 0xC6, 0x40, 0x14, 0x72, 0xC6, 0x40, 0x15, 0x00, 0x49, 0x89, 0xC4, 0x48, 0x8B, 0x8B, 0xC0, 0x01, 0x00, 0x00, 0x48, 0x89, 0xC2, 0xFF, 0x93, 0xB0, 0x01, 0x00, 0x00, 0x48, 0x83, 0xF8, 0x00, 0x0F, 0x84, 0xF9, 0x00, 0x00, 0x00, 0x48, 0x89, 0x83, 0xE8, 0x01, 0x00, 0x00, 0x4C, 0x89, 0xE1, 0xE8, 0x81, 0xFA, 0xFF, 0xFF, 0xB9, 0x0C, 0x00, 0x00, 0x00, 0xE8, 0x4E, 0xFA, 0xFF, 0xFF, 0xC6, 0x00, 0x56, 0xC6, 0x40, 0x01, 0x61, 0xC6, 0x40, 0x02, 0x72, 0xC6, 0x40, 0x03, 0x69, 0xC6, 0x40, 0x04, 0x61, 0xC6, 0x40, 0x05, 0x6E, 0xC6, 0x40, 0x06, 0x74, 0xC6, 0x40, 0x07, 0x49, 0xC6, 0x40, 0x08, 0x6E, 0xC6, 0x40, 0x09, 0x69, 0xC6, 0x40, 0x0A, 0x74, 0xC6, 0x40, 0x0B, 0x00, 0x49, 0x89, 0xC4, 0x48, 0x8B, 0x8B, 0xC0, 0x01, 0x00, 0x00, 0x48, 0x89, 0xC2, 0xFF, 0x93, 0xB0, 0x01, 0x00, 0x00, 0x48, 0x83, 0xF8, 0x00, 0x0F, 0x84, 0x94, 0x00, 0x00, 0x00, 0x48, 0x89, 0x83, 0xF0, 0x01, 0x00, 0x00, 0x4C, 0x89, 0xE1, 0xE8, 0x1C, 0xFA, 0xFF, 0xFF, 0xB9, 0x12, 0x00, 0x00, 0x00, 0xE8, 0xE9, 0xF9, 0xFF, 0xFF, 0xC6, 0x00, 0x43, 0xC6, 0x40, 0x01, 0x4C, 0xC6, 0x40, 0x02, 0x52, 0xC6, 0x40, 0x03, 0x43, 0xC6, 0x40, 0x04, 0x72, 0xC6, 0x40, 0x05, 0x65, 0xC6, 0x40, 0x06, 0x61, 0xC6, 0x40, 0x07, 0x74, 0xC6, 0x40, 0x08, 0x65, 0xC6, 0x40, 0x09, 0x49, 0xC6, 0x40, 0x0A, 0x6E, 0xC6, 0x40, 0x0B, 0x73, 0xC6, 0x40, 0x0C, 0x74, 0xC6, 0x40, 0x0D, 0x61, 0xC6, 0x40, 0x0E, 0x6E, 0xC6, 0x40, 0x0F, 0x63, 0xC6, 0x40, 0x10, 0x65, 0xC6, 0x40, 0x11, 0x00, 0x49, 0x89, 0xC4, 0x48, 0x8B, 0x8B, 0xB8, 0x01, 0x00, 0x00, 0x48, 0x89, 0xC2, 0xFF, 0x93, 0xB0, 0x01, 0x00, 0x00, 0x48, 0x83, 0xF8, 0x00, 0x74, 0x1B, 0x48, 0x89, 0x83, 0xF8, 0x01, 0x00, 0x00, 0x4C, 0x89, 0xE1, 0xE8, 0xA3, 0xF9, 0xFF, 0xFF, 0xB8, 0x01, 0x00, 0x00, 0x00, 0x41, 0x5C, 0x48, 0x89, 0xEC, 0x5D, 0xC3, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x41, 0x5C, 0x48, 0x89, 0xEC, 0x5D, 0xC3
        };

        private static byte[] InjectAssembly32Bit = new byte[]//see "32bit inject c# run.asm"
        {
            0x8B, 0x44, 0x24, 0x04, 0x89, 0xC6, 0xFF, 0x56, 0x08, 0x68, 0x00, 0x04, 0x00, 0x00, 0x6A, 0x08, 0x50, 0xFF, 0x16, 0x89, 0xC3, 0x8B, 0x06, 0x89, 0x83, 0x2C, 0x01, 0x00, 0x00, 0x8B, 0x46, 0x04, 0x89, 0x83, 0x30, 0x01, 0x00, 0x00, 0x8B, 0x46, 0x08, 0x89, 0x83, 0x34, 0x01, 0x00, 0x00, 0x8B, 0x46, 0x0C, 0x89, 0x83, 0x38, 0x01, 0x00, 0x00, 0x8B, 0x46, 0x10, 0x89, 0x83, 0x3C, 0x01, 0x00, 0x00, 0x8B, 0x46, 0x14, 0x89, 0x83, 0x90, 0x01, 0x00, 0x00, 0x8B, 0x46, 0x18, 0x89, 0x83, 0x94, 0x01, 0x00, 0x00, 0x89, 0xF0, 0x83, 0xC0, 0x20, 0x89, 0x83, 0xC8, 0x00, 0x00, 0x00, 0x89, 0xF0, 0x83, 0xC0, 0x20, 0x03, 0x46, 0x1C, 0x83, 0xC0, 0x04, 0x89, 0x83, 0xCC, 0x00, 0x00, 0x00, 0xE8, 0xD2, 0x02, 0x00, 0x00, 0x83, 0xF8, 0x00, 0x0F, 0x84, 0xC1, 0x02, 0x00, 0x00, 0x89, 0x5B, 0x04, 0xE8, 0xA1, 0x06, 0x00, 0x00, 0x89, 0x43, 0x08, 0xE8, 0xD3, 0x06, 0x00, 0x00, 0x89, 0x43, 0x0C, 0xFF, 0x73, 0x04, 0xFF, 0x73, 0x0C, 0xFF, 0x73, 0x08, 0xFF, 0x93, 0x60, 0x01, 0x00, 0x00, 0x83, 0xF8, 0x00, 0x0F, 0x85, 0x96, 0x02, 0x00, 0x00, 0x8B, 0x03, 0x8B, 0x00, 0x8B, 0x40, 0x14, 0x89, 0x43, 0x0E, 0x89, 0xD8, 0x83, 0xC0, 0x12, 0x89, 0x43, 0x16, 0xFF, 0x73, 0x16, 0xFF, 0x33, 0xFF, 0x53, 0x0E, 0x83, 0xF8, 0x00, 0x0F, 0x85, 0x73, 0x02, 0x00, 0x00, 0xE8, 0xCA, 0x06, 0x00, 0x00, 0x89, 0x43, 0x1A, 0x89, 0xD8, 0x83, 0xC0, 0x1E, 0x89, 0x43, 0x22, 0x89, 0xD8, 0x83, 0xC0, 0x26, 0x89, 0x43, 0x2A, 0x8B, 0x43, 0x12, 0x8B, 0x00, 0x8B, 0x40, 0x0C, 0x89, 0x43, 0x2E, 0x6A, 0x00, 0xFF, 0x73, 0x2A, 0x6A, 0x01, 0xFF, 0x73, 0x12, 0xFF, 0x53, 0x2E, 0x83, 0xF8, 0x00, 0x75, 0x14, 0x8B, 0x43, 0x26, 0x8B, 0x00, 0x8B, 0x00, 0xFF, 0x73, 0x22, 0xFF, 0x73, 0x1A, 0xFF, 0x73, 0x26, 0xFF, 0xD0, 0xEB, 0xDA, 0x83, 0x7B, 0x1E, 0x00, 0x0F, 0x84, 0x20, 0x02, 0x00, 0x00, 0x8B, 0x43, 0x1E, 0x8B, 0x00, 0x8B, 0x40, 0x24, 0x89, 0x43, 0x32, 0xE8, 0xA6, 0x06, 0x00, 0x00, 0x89, 0x43, 0x36, 0xE8, 0xD8, 0x06, 0x00, 0x00, 0x89, 0x43, 0x3A, 0x89, 0xD8, 0x83, 0xC0, 0x3E, 0x89, 0x43, 0x42, 0xFF, 0x73, 0x42, 0xFF, 0x73, 0x36, 0xFF, 0x73, 0x3A, 0xFF, 0x73, 0x1E, 0xFF, 0x53, 0x32, 0x83, 0xF8, 0x00, 0x0F, 0x85, 0xE5, 0x01, 0x00, 0x00, 0x83, 0x7B, 0x3E, 0x00, 0x0F, 0x84, 0xDB, 0x01, 0x00, 0x00, 0x8B, 0x43, 0x3E, 0x8B, 0x00, 0x8B, 0x40, 0x28, 0x89, 0x43, 0x46, 0xFF, 0x73, 0x3E, 0xFF, 0x53, 0x46, 0x83, 0xF8, 0x00, 0x0F, 0x85, 0xC1, 0x01, 0x00, 0x00, 0x8B, 0x43, 0x3E, 0x8B, 0x00, 0x8B, 0x40, 0x34, 0x89, 0x43, 0x4A, 0x89, 0xD8, 0x83, 0xC0, 0x4E, 0x89, 0x43, 0x52, 0xE8, 0xB3, 0x06, 0x00, 0x00, 0x89, 0x43, 0x56, 0xFF, 0x73, 0x52, 0xFF, 0x73, 0x3E, 0xFF, 0x53, 0x4A, 0x83, 0x7B, 0x4E, 0x00, 0x0F, 0x84, 0x93, 0x01, 0x00, 0x00, 0x89, 0xD8, 0x83, 0xC0, 0x5A, 0x89, 0x43, 0x5E, 0x8B, 0x43, 0x4E, 0x8B, 0x00, 0x8B, 0x00, 0x89, 0x43, 0x62, 0xFF, 0x73, 0x5E, 0xFF, 0x73, 0x56, 0xFF, 0x73, 0x4E, 0xFF, 0x53, 0x62, 0x83, 0x7B, 0x5A, 0x00, 0x0F, 0x84, 0x6B, 0x01, 0x00, 0x00, 0x8B, 0x83, 0x90, 0x01, 0x00, 0x00, 0x89, 0x43, 0x66, 0x8B, 0x83, 0x94, 0x01, 0x00, 0x00, 0x89, 0x43, 0x6A, 0x6A, 0x08, 0xE8, 0x04, 0x05, 0x00, 0x00, 0x89, 0x43, 0x6E, 0x8B, 0x43, 0x6E, 0x8B, 0x7B, 0x6A, 0x89, 0x38, 0xC7, 0x40, 0x04, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x73, 0x6E, 0x6A, 0x01, 0x6A, 0x11, 0xFF, 0x93, 0x48, 0x01, 0x00, 0x00, 0x89, 0x43, 0x72, 0xFF, 0x73, 0x72, 0xFF, 0x93, 0x4C, 0x01, 0x00, 0x00, 0x8B, 0x43, 0x72, 0x83, 0xC0, 0x0C, 0xFF, 0x30, 0xFF, 0x73, 0x66, 0xFF, 0x73, 0x6A, 0xE8, 0xA8, 0x04, 0x00, 0x00, 0xFF, 0x73, 0x72, 0xFF, 0x93, 0x50, 0x01, 0x00, 0x00, 0x89, 0xD8, 0x83, 0xC0, 0x76, 0x89, 0x43, 0x7A, 0x8B, 0x43, 0x5A, 0x8B, 0x00, 0x8B, 0x80, 0xB4, 0x00, 0x00, 0x00, 0x89, 0x43, 0x7E, 0xFF, 0x73, 0x7A, 0xFF, 0x73, 0x72, 0xFF, 0x73, 0x5A, 0xFF, 0x53, 0x7E, 0x83, 0xF8, 0x00, 0x0F, 0x85, 0xE0, 0x00, 0x00, 0x00, 0x83, 0x7B, 0x76, 0x00, 0x0F, 0x84, 0xD6, 0x00, 0x00, 0x00, 0x89, 0xD8, 0x05, 0x82, 0x00, 0x00, 0x00, 0x89, 0x83, 0x86, 0x00, 0x00, 0x00, 0xE8, 0x08, 0x06, 0x00, 0x00, 0x89, 0x83, 0x8A, 0x00, 0x00, 0x00, 0x8B, 0x43, 0x76, 0x8B, 0x00, 0x8B, 0x40, 0x44, 0x89, 0x83, 0x8E, 0x00, 0x00, 0x00, 0xFF, 0xB3, 0x86, 0x00, 0x00, 0x00, 0xFF, 0xB3, 0x8A, 0x00, 0x00, 0x00, 0xFF, 0x73, 0x76, 0xFF, 0x93, 0x8E, 0x00, 0x00, 0x00, 0x83, 0xF8, 0x00, 0x0F, 0x85, 0x92, 0x00, 0x00, 0x00, 0x83, 0xBB, 0x82, 0x00, 0x00, 0x00, 0x00, 0x0F, 0x84, 0x85, 0x00, 0x00, 0x00, 0x6A, 0x00, 0x6A, 0x00, 0x6A, 0x0C, 0xFF, 0x93, 0x58, 0x01, 0x00, 0x00, 0x89, 0x83, 0x92, 0x00, 0x00, 0x00, 0xE8, 0xCE, 0x05, 0x00, 0x00, 0x89, 0x83, 0x96, 0x00, 0x00, 0x00, 0xE8, 0xC3, 0x05, 0x00, 0x00, 0x89, 0x83, 0x9A, 0x00, 0x00, 0x00, 0x8B, 0x83, 0x82, 0x00, 0x00, 0x00, 0x8B, 0x00, 0x8B, 0x80, 0xE4, 0x00, 0x00, 0x00, 0x89, 0x83, 0x9E, 0x00, 0x00, 0x00, 0xE8, 0x96, 0x05, 0x00, 0x00, 0x89, 0x83, 0xA2, 0x00, 0x00, 0x00, 0xFF, 0xB3, 0x96, 0x00, 0x00, 0x00, 0xFF, 0xB3, 0x92, 0x00, 0x00, 0x00, 0x8B, 0x83, 0x9A, 0x00, 0x00, 0x00, 0xFF, 0x70, 0x0C, 0xFF, 0x70, 0x08, 0xFF, 0x70, 0x04, 0xFF, 0x30, 0x6A, 0x00, 0x68, 0x18, 0x01, 0x00, 0x00, 0xFF, 0xB3, 0xA2, 0x00, 0x00, 0x00, 0xFF, 0xB3, 0x82, 0x00, 0x00, 0x00, 0xFF, 0x93, 0x9E, 0x00, 0x00, 0x00, 0x83, 0xF8, 0x00, 0x75, 0x03, 0xC2, 0x04, 0x00, 0xB8, 0xFF, 0xFF, 0xFF, 0xFF, 0xC2, 0x04, 0x00, 0x6A, 0x0C, 0xE8, 0xA3, 0x03, 0x00, 0x00, 0xC6, 0x00, 0x6D, 0xC6, 0x40, 0x01, 0x73, 0xC6, 0x40, 0x02, 0x63, 0xC6, 0x40, 0x03, 0x6F, 0xC6, 0x40, 0x04, 0x72, 0xC6, 0x40, 0x05, 0x65, 0xC6, 0x40, 0x06, 0x65, 0xC6, 0x40, 0x07, 0x2E, 0xC6, 0x40, 0x08, 0x64, 0xC6, 0x40, 0x09, 0x6C, 0xC6, 0x40, 0x0A, 0x6C, 0xC6, 0x40, 0x0B, 0x00, 0x50, 0x50, 0xFF, 0x93, 0x38, 0x01, 0x00, 0x00, 0x89, 0x83, 0x40, 0x01, 0x00, 0x00, 0x58, 0x50, 0xE8, 0x7C, 0x03, 0x00, 0x00, 0x6A, 0x0D, 0xE8, 0x58, 0x03, 0x00, 0x00, 0xC6, 0x00, 0x6F, 0xC6, 0x40, 0x01, 0x6C, 0xC6, 0x40, 0x02, 0x65, 0xC6, 0x40, 0x03, 0x61, 0xC6, 0x40, 0x04, 0x75, 0xC6, 0x40, 0x05, 0x74, 0xC6, 0x40, 0x06, 0x33, 0xC6, 0x40, 0x07, 0x32, 0xC6, 0x40, 0x08, 0x2E, 0xC6, 0x40, 0x09, 0x64, 0xC6, 0x40, 0x0A, 0x6C, 0xC6, 0x40, 0x0B, 0x6C, 0xC6, 0x40, 0x0C, 0x00, 0x50, 0x50, 0xFF, 0x93, 0x38, 0x01, 0x00, 0x00, 0x89, 0x83, 0x44, 0x01, 0x00, 0x00, 0x58, 0x50, 0xE8, 0x2D, 0x03, 0x00, 0x00, 0x6A, 0x10, 0xE8, 0x09, 0x03, 0x00, 0x00, 0xC6, 0x00, 0x53, 0xC6, 0x40, 0x01, 0x61, 0xC6, 0x40, 0x02, 0x66, 0xC6, 0x40, 0x03, 0x65, 0xC6, 0x40, 0x04, 0x41, 0xC6, 0x40, 0x05, 0x72, 0xC6, 0x40, 0x06, 0x72, 0xC6, 0x40, 0x07, 0x61, 0xC6, 0x40, 0x08, 0x79, 0xC6, 0x40, 0x09, 0x43, 0xC6, 0x40, 0x0A, 0x72, 0xC6, 0x40, 0x0B, 0x65, 0xC6, 0x40, 0x0C, 0x61, 0xC6, 0x40, 0x0D, 0x74, 0xC6, 0x40, 0x0E, 0x65, 0xC6, 0x40, 0x0F, 0x00, 0x50, 0x50, 0xFF, 0xB3, 0x44, 0x01, 0x00, 0x00, 0xFF, 0x93, 0x3C, 0x01, 0x00, 0x00, 0x83, 0xF8, 0x00, 0x0F, 0x84, 0x8F, 0x02, 0x00, 0x00, 0x89, 0x83, 0x48, 0x01, 0x00, 0x00, 0x58, 0x50, 0xE8, 0xC3, 0x02, 0x00, 0x00, 0x6A, 0x0E, 0xE8, 0x9F, 0x02, 0x00, 0x00, 0xC6, 0x00, 0x53, 0xC6, 0x40, 0x01, 0x61, 0xC6, 0x40, 0x02, 0x66, 0xC6, 0x40, 0x03, 0x65, 0xC6, 0x40, 0x04, 0x41, 0xC6, 0x40, 0x05, 0x72, 0xC6, 0x40, 0x06, 0x72, 0xC6, 0x40, 0x07, 0x61, 0xC6, 0x40, 0x08, 0x79, 0xC6, 0x40, 0x09, 0x4C, 0xC6, 0x40, 0x0A, 0x6F, 0xC6, 0x40, 0x0B, 0x63, 0xC6, 0x40, 0x0C, 0x6B, 0xC6, 0x40, 0x0D, 0x00, 0x50, 0x50, 0xFF, 0xB3, 0x44, 0x01, 0x00, 0x00, 0xFF, 0x93, 0x3C, 0x01, 0x00, 0x00, 0x83, 0xF8, 0x00, 0x0F, 0x84, 0x2D, 0x02, 0x00, 0x00, 0x89, 0x83, 0x4C, 0x01, 0x00, 0x00, 0x58, 0x50, 0xE8, 0x61, 0x02, 0x00, 0x00, 0x6A, 0x10, 0xE8, 0x3D, 0x02, 0x00, 0x00, 0xC6, 0x00, 0x53, 0xC6, 0x40, 0x01, 0x61, 0xC6, 0x40, 0x02, 0x66, 0xC6, 0x40, 0x03, 0x65, 0xC6, 0x40, 0x04, 0x41, 0xC6, 0x40, 0x05, 0x72, 0xC6, 0x40, 0x06, 0x72, 0xC6, 0x40, 0x07, 0x61, 0xC6, 0x40, 0x08, 0x79, 0xC6, 0x40, 0x09, 0x55, 0xC6, 0x40, 0x0A, 0x6E, 0xC6, 0x40, 0x0B, 0x6C, 0xC6, 0x40, 0x0C, 0x6F, 0xC6, 0x40, 0x0D, 0x63, 0xC6, 0x40, 0x0E, 0x6B, 0xC6, 0x40, 0x0F, 0x00, 0x50, 0x50, 0xFF, 0xB3, 0x44, 0x01, 0x00, 0x00, 0xFF, 0x93, 0x3C, 0x01, 0x00, 0x00, 0x83, 0xF8, 0x00, 0x0F, 0x84, 0xC3, 0x01, 0x00, 0x00, 0x89, 0x83, 0x50, 0x01, 0x00, 0x00, 0x58, 0x50, 0xE8, 0xF7, 0x01, 0x00, 0x00, 0x6A, 0x0F, 0xE8, 0xD3, 0x01, 0x00, 0x00, 0xC6, 0x00, 0x53, 0xC6, 0x40, 0x01, 0x79, 0xC6, 0x40, 0x02, 0x73, 0xC6, 0x40, 0x03, 0x41, 0xC6, 0x40, 0x04, 0x6C, 0xC6, 0x40, 0x05, 0x6C, 0xC6, 0x40, 0x06, 0x6F, 0xC6, 0x40, 0x07, 0x63, 0xC6, 0x40, 0x08, 0x53, 0xC6, 0x40, 0x09, 0x74, 0xC6, 0x40, 0x0A, 0x72, 0xC6, 0x40, 0x0B, 0x69, 0xC6, 0x40, 0x0C, 0x6E, 0xC6, 0x40, 0x0D, 0x67, 0xC6, 0x40, 0x0E, 0x00, 0x50, 0x50, 0xFF, 0xB3, 0x44, 0x01, 0x00, 0x00, 0xFF, 0x93, 0x3C, 0x01, 0x00, 0x00, 0x83, 0xF8, 0x00, 0x0F, 0x84, 0x5D, 0x01, 0x00, 0x00, 0x89, 0x83, 0x54, 0x01, 0x00, 0x00, 0x58, 0x50, 0xE8, 0x91, 0x01, 0x00, 0x00, 0x6A, 0x16, 0xE8, 0x6D, 0x01, 0x00, 0x00, 0xC6, 0x00, 0x53, 0xC6, 0x40, 0x01, 0x61, 0xC6, 0x40, 0x02, 0x66, 0xC6, 0x40, 0x03, 0x65, 0xC6, 0x40, 0x04, 0x41, 0xC6, 0x40, 0x05, 0x72, 0xC6, 0x40, 0x06, 0x72, 0xC6, 0x40, 0x07, 0x61, 0xC6, 0x40, 0x08, 0x79, 0xC6, 0x40, 0x09, 0x43, 0xC6, 0x40, 0x0A, 0x72, 0xC6, 0x40, 0x0B, 0x65, 0xC6, 0x40, 0x0C, 0x61, 0xC6, 0x40, 0x0D, 0x74, 0xC6, 0x40, 0x0E, 0x65, 0xC6, 0x40, 0x0F, 0x56, 0xC6, 0x40, 0x10, 0x65, 0xC6, 0x40, 0x11, 0x63, 0xC6, 0x40, 0x12, 0x74, 0xC6, 0x40, 0x13, 0x6F, 0xC6, 0x40, 0x14, 0x72, 0xC6, 0x40, 0x15, 0x00, 0x50, 0x50, 0xFF, 0xB3, 0x44, 0x01, 0x00, 0x00, 0xFF, 0x93, 0x3C, 0x01, 0x00, 0x00, 0x83, 0xF8, 0x00, 0x0F, 0x84, 0xDB, 0x00, 0x00, 0x00, 0x89, 0x83, 0x58, 0x01, 0x00, 0x00, 0x58, 0x50, 0xE8, 0x0F, 0x01, 0x00, 0x00, 0x6A, 0x0C, 0xE8, 0xEB, 0x00, 0x00, 0x00, 0xC6, 0x00, 0x56, 0xC6, 0x40, 0x01, 0x61, 0xC6, 0x40, 0x02, 0x72, 0xC6, 0x40, 0x03, 0x69, 0xC6, 0x40, 0x04, 0x61, 0xC6, 0x40, 0x05, 0x6E, 0xC6, 0x40, 0x06, 0x74, 0xC6, 0x40, 0x07, 0x49, 0xC6, 0x40, 0x08, 0x6E, 0xC6, 0x40, 0x09, 0x69, 0xC6, 0x40, 0x0A, 0x74, 0xC6, 0x40, 0x0B, 0x00, 0x50, 0x50, 0xFF, 0xB3, 0x44, 0x01, 0x00, 0x00, 0xFF, 0x93, 0x3C, 0x01, 0x00, 0x00, 0x83, 0xF8, 0x00, 0x0F, 0x84, 0x81, 0x00, 0x00, 0x00, 0x89, 0x83, 0x5C, 0x01, 0x00, 0x00, 0x58, 0x50, 0xE8, 0xB5, 0x00, 0x00, 0x00, 0x6A, 0x12, 0xE8, 0x91, 0x00, 0x00, 0x00, 0xC6, 0x00, 0x43, 0xC6, 0x40, 0x01, 0x4C, 0xC6, 0x40, 0x02, 0x52, 0xC6, 0x40, 0x03, 0x43, 0xC6, 0x40, 0x04, 0x72, 0xC6, 0x40, 0x05, 0x65, 0xC6, 0x40, 0x06, 0x61, 0xC6, 0x40, 0x07, 0x74, 0xC6, 0x40, 0x08, 0x65, 0xC6, 0x40, 0x09, 0x49, 0xC6, 0x40, 0x0A, 0x6E, 0xC6, 0x40, 0x0B, 0x73, 0xC6, 0x40, 0x0C, 0x74, 0xC6, 0x40, 0x0D, 0x61, 0xC6, 0x40, 0x0E, 0x6E, 0xC6, 0x40, 0x0F, 0x63, 0xC6, 0x40, 0x10, 0x65, 0xC6, 0x40, 0x11, 0x00, 0x50, 0x50, 0xFF, 0xB3, 0x40, 0x01, 0x00, 0x00, 0xFF, 0x93, 0x3C, 0x01, 0x00, 0x00, 0x83, 0xF8, 0x00, 0x74, 0x13, 0x89, 0x83, 0x60, 0x01, 0x00, 0x00, 0x58, 0x50, 0xE8, 0x47, 0x00, 0x00, 0x00, 0xB8, 0x01, 0x00, 0x00, 0x00, 0xC3, 0xB8, 0x00, 0x00, 0x00, 0x00, 0xC3, 0x57, 0x56, 0x51, 0x8B, 0x4C, 0x24, 0x10, 0x8B, 0x74, 0x24, 0x14, 0x8B, 0x7C, 0x24, 0x18, 0x8A, 0x06, 0x88, 0x07, 0x46, 0x47, 0x49, 0x75, 0xF7, 0x59, 0x5E, 0x5F, 0xC2, 0x0C, 0x00, 0x8B, 0x44, 0x24, 0x04, 0x51, 0x52, 0x89, 0xC1, 0xFF, 0x93, 0x34, 0x01, 0x00, 0x00, 0x51, 0x6A, 0x08, 0x50, 0xFF, 0x93, 0x2C, 0x01, 0x00, 0x00, 0x5A, 0x59, 0xC2, 0x04, 0x00, 0x55, 0x8B, 0x6C, 0x24, 0x08, 0xFF, 0x93, 0x34, 0x01, 0x00, 0x00, 0x55, 0x6A, 0x00, 0x50, 0xFF, 0x93, 0x30, 0x01, 0x00, 0x00, 0x5D, 0xC2, 0x04, 0x00, 0x6A, 0x10, 0xE8, 0xC3, 0xFF, 0xFF, 0xFF, 0xC7, 0x00, 0x8D, 0x18, 0x80, 0x92, 0x66, 0xC7, 0x40, 0x04, 0x8E, 0x0E, 0x66, 0xC7, 0x40, 0x06, 0x67, 0x48, 0xC6, 0x40, 0x08, 0xB3, 0xC6, 0x40, 0x09, 0x0C, 0xC6, 0x40, 0x0A, 0x7F, 0xC6, 0x40, 0x0B, 0xA8, 0xC6, 0x40, 0x0C, 0x38, 0xC6, 0x40, 0x0D, 0x84, 0xC6, 0x40, 0x0E, 0xE8, 0xC6, 0x40, 0x0F, 0xDE, 0xC3, 0x6A, 0x10, 0xE8, 0x89, 0xFF, 0xFF, 0xFF, 0xC7, 0x00, 0x9E, 0xDB, 0x32, 0xD3, 0x66, 0xC7, 0x40, 0x04, 0xB3, 0xB9, 0x66, 0xC7, 0x40, 0x06, 0x25, 0x41, 0xC6, 0x40, 0x08, 0x82, 0xC6, 0x40, 0x09, 0x07, 0xC6, 0x40, 0x0A, 0xA1, 0xC6, 0x40, 0x0B, 0x48, 0xC6, 0x40, 0x0C, 0x84, 0xC6, 0x40, 0x0D, 0xF5, 0xC6, 0x40, 0x0E, 0x32, 0xC6, 0x40, 0x0F, 0x16, 0xC3, 0x6A, 0x10, 0xE8, 0x4F, 0xFF, 0xFF, 0xFF, 0xC7, 0x00, 0xD2, 0xD1, 0x39, 0xBD, 0x66, 0xC7, 0x40, 0x04, 0x2F, 0xBA, 0x66, 0xC7, 0x40, 0x06, 0x6A, 0x48, 0xC6, 0x40, 0x08, 0x89, 0xC6, 0x40, 0x09, 0xB0, 0xC6, 0x40, 0x0A, 0xB4, 0xC6, 0x40, 0x0B, 0xB0, 0xC6, 0x40, 0x0C, 0xCB, 0xC6, 0x40, 0x0D, 0x46, 0xC6, 0x40, 0x0E, 0x68, 0xC6, 0x40, 0x0F, 0x91, 0xC3, 0x6A, 0x10, 0xE8, 0x15, 0xFF, 0xFF, 0xFF, 0xC7, 0x00, 0x22, 0x67, 0x2F, 0xCB, 0x66, 0xC7, 0x40, 0x04, 0x3A, 0xAB, 0x66, 0xC7, 0x40, 0x06, 0xD2, 0x11, 0xC6, 0x40, 0x08, 0x9C, 0xC6, 0x40, 0x09, 0x40, 0xC6, 0x40, 0x0A, 0x00, 0xC6, 0x40, 0x0B, 0xC0, 0xC6, 0x40, 0x0C, 0x4F, 0xC6, 0x40, 0x0D, 0xA3, 0xC6, 0x40, 0x0E, 0x0A, 0xC6, 0x40, 0x0F, 0x3E, 0xC3, 0x6A, 0x10, 0xE8, 0xDB, 0xFE, 0xFF, 0xFF, 0xC7, 0x00, 0x23, 0x67, 0x2F, 0xCB, 0x66, 0xC7, 0x40, 0x04, 0x3A, 0xAB, 0x66, 0xC7, 0x40, 0x06, 0xD2, 0x11, 0xC6, 0x40, 0x08, 0x9C, 0xC6, 0x40, 0x09, 0x40, 0xC6, 0x40, 0x0A, 0x00, 0xC6, 0x40, 0x0B, 0xC0, 0xC6, 0x40, 0x0C, 0x4F, 0xC6, 0x40, 0x0D, 0xA3, 0xC6, 0x40, 0x0E, 0x0A, 0xC6, 0x40, 0x0F, 0x3E, 0xC3, 0x6A, 0x10, 0xE8, 0xA1, 0xFE, 0xFF, 0xFF, 0xC7, 0x00, 0xDC, 0x96, 0xF6, 0x05, 0x66, 0xC7, 0x40, 0x04, 0x29, 0x2B, 0x66, 0xC7, 0x40, 0x06, 0x63, 0x36, 0xC6, 0x40, 0x08, 0xAD, 0xC6, 0x40, 0x09, 0x8B, 0xC6, 0x40, 0x0A, 0xC4, 0xC6, 0x40, 0x0B, 0x38, 0xC6, 0x40, 0x0C, 0x9C, 0xC6, 0x40, 0x0D, 0xF2, 0xC6, 0x40, 0x0E, 0xA7, 0xC6, 0x40, 0x0F, 0x13, 0xC3, 0x8B, 0x83, 0xC8, 0x00, 0x00, 0x00, 0x50, 0xFF, 0x93, 0x54, 0x01, 0x00, 0x00, 0xC3, 0x8B, 0x83, 0xCC, 0x00, 0x00, 0x00, 0x50, 0xFF, 0x93, 0x54, 0x01, 0x00, 0x00, 0xC3, 0x6A, 0x10, 0xE8, 0x4B, 0xFE, 0xFF, 0xFF, 0x50, 0xFF, 0x93, 0x5C, 0x01, 0x00, 0x00, 0xC3
        };

        private static byte[] SelfByteCache;

        public enum DllArchitecture
        {
            X86,
            X64,
            AnyCpu
        }

        public enum InjectionStatusCode
        {
            SUCCESS,
            COULDNT_OPEN_PROCESS,
            WRONG_DLL_ARCH_FOR_PROCESS_ARCH,
            COULDNT_WRITE_TO_PROCESS,
            COULDNT_CREATE_REMOTE_THREAD,
            COULDNT_GET_REMOTE_PROCEDURE_HANDLE,
            SHELLCODE_RETURNED_BAD_RESULT,
            HEAVENSGATE_NON_OPERATIONAL,
            COULDNT_GET_NTDLL_FROM_HEAVENSGATE,
            EXCEEDED_TIMEOUT,
            COULDNT_GET_EXITCODE
        }

        public static IntPtr GetProcessHandleWithRequiredRights(int pid)
        {
            uint PROCESS_CREATE_THREAD = 0x0002;
            uint PROCESS_QUERY_INFORMATION = 0x0400;
            uint PROCESS_VM_OPERATION = 0x0008;
            uint PROCESS_VM_WRITE = 0x0020;
            uint PROCESS_VM_READ = 0x0010;

            uint REQUIRED_ACCESS = PROCESS_CREATE_THREAD | PROCESS_QUERY_INFORMATION | PROCESS_VM_OPERATION | PROCESS_VM_WRITE | PROCESS_VM_READ;

            IntPtr handle = NativeMethods.OpenProcess(REQUIRED_ACCESS, false, (uint)pid);
            return handle;
        }

        public static bool IsProcess64Bit(int pid, out bool worked)
        {
            worked = false;
            uint PROCESS_QUERY_LIMITED_INFORMATION = 0x1000;
            IntPtr handle = NativeMethods.OpenProcess(PROCESS_QUERY_LIMITED_INFORMATION, false, (uint)pid);
            if (handle == IntPtr.Zero)
            {
                return false;
            }
            bool result = Utils.IsProcess64Bit(handle);
            NativeMethods.CloseHandle(handle);
            worked = true;
            return result;
        }

        public static InjectionStatusCode Inject(int PID, byte[] DllBytes, string NameSpace, string Class, string Method, DllArchitecture dllArchitecture, uint MaxShellCodeWaitTime = 1000)
        {
            IntPtr ProcessHandle = GetProcessHandleWithRequiredRights(PID);
            if (ProcessHandle == IntPtr.Zero)
            {
                return InjectionStatusCode.COULDNT_OPEN_PROCESS;
            }
            InjectionStatusCode result = Inject(ProcessHandle, DllBytes, NameSpace, Class, Method, dllArchitecture, MaxShellCodeWaitTime);
            NativeMethods.CloseHandle(ProcessHandle);
            return result;
        }

        public static InjectionStatusCode Inject(IntPtr ProcessHandle, byte[] DllBytes, string NameSpace, string Class, string Method, DllArchitecture dllArchitecture, uint MaxShellCodeWaitTime = 1000)
        {
            bool is64BitProc = Utils.IsProcess64Bit(ProcessHandle);
            if (is64BitProc)
            {
                if (dllArchitecture == DllArchitecture.X86)
                {
                    return InjectionStatusCode.WRONG_DLL_ARCH_FOR_PROCESS_ARCH;
                }
            }
            else
            {
                if (dllArchitecture == DllArchitecture.X64)
                {
                    return InjectionStatusCode.WRONG_DLL_ARCH_FOR_PROCESS_ARCH;
                }
            }

            if (is64BitProc)
            {
                return Inject64(ProcessHandle, DllBytes, NameSpace, Class, Method, MaxShellCodeWaitTime);
            }
            else
            {
                return Inject32(ProcessHandle, DllBytes, NameSpace, Class, Method, MaxShellCodeWaitTime);
            }
        }

        public static InjectionStatusCode Inject(IntPtr ProcessHandle, Action function, uint MaxShellCodeWaitTime = 1000, byte[] ExtraDataAtInjectEnd = null)
        {
            if (!function.Method.IsStatic)
            {
                throw new Exception("the supplied method needs to be static!");
            }

            if (SelfByteCache == null)
            {
                SelfByteCache = Utils.GetCurrentSelfBytes();
            }
            byte[] DLL = SelfByteCache;
            if (ExtraDataAtInjectEnd != null)
            {
                // 当前程序 byte[] + 参数
                DLL = new byte[SelfByteCache.Length + ExtraDataAtInjectEnd.Length];
                Buffer.BlockCopy(SelfByteCache, 0, DLL, 0, SelfByteCache.Length);
                Buffer.BlockCopy(ExtraDataAtInjectEnd, 0, DLL, SelfByteCache.Length, ExtraDataAtInjectEnd.Length);
            }

            return Inject(ProcessHandle, DLL, function.Method.DeclaringType.Namespace, function.Method.DeclaringType.Name, function.Method.Name, GetCurrentArch(), MaxShellCodeWaitTime);

        }

        public static InjectionStatusCode Inject(int PID, Action function, uint MaxShellCodeWaitTimeMilli = 1000, byte[] ExtraDataAtInjectEnd = null)
        {
            IntPtr ProcessHandle = GetProcessHandleWithRequiredRights(PID);
            if (ProcessHandle == IntPtr.Zero)
            {
                return InjectionStatusCode.COULDNT_OPEN_PROCESS;
            }
            InjectionStatusCode result = Inject(ProcessHandle, function, MaxShellCodeWaitTimeMilli, ExtraDataAtInjectEnd);
            NativeMethods.CloseHandle(ProcessHandle);
            return result;
        }

        private static DllArchitecture GetCurrentArch()
        {
            Assembly.GetExecutingAssembly().ManifestModule.GetPEKind(out PortableExecutableKinds peKind, out ImageFileMachine machine);

            if ((peKind & PortableExecutableKinds.PE32Plus) == PortableExecutableKinds.PE32Plus)
            {
                if (machine == ImageFileMachine.AMD64 || machine == ImageFileMachine.IA64)
                {
                    return DllArchitecture.X64;
                }
            }
            else
            {
                if (machine == ImageFileMachine.I386)
                {
                    if ((peKind & PortableExecutableKinds.Required32Bit) == PortableExecutableKinds.Required32Bit)
                    {
                        return DllArchitecture.X86;
                    }
                    else
                    {
                        return DllArchitecture.AnyCpu;
                    }
                }
            }
            return DllArchitecture.AnyCpu;//just try i guess.

        }


        private static InjectionStatusCode Inject32(IntPtr ProcessHandle, byte[] injectingFile, string NameSpace, string Class, string FunctionName, uint MaxWaitTime = 1000)
        {
            uint MEM_COMMIT = 0x00001000;
            uint MEM_RESERVE = 0x00002000;
            uint PAGE_EXECUTE_READWRITE = 0x40;
            uint PAGE_READWRITE = 0x04;
            uint THREAD_ALL_ACCESS = 0x1FFFFF;
            string NameSpaceAndClass = NameSpace + "." + Class;

            IntPtr WriteAddress = NativeMethods.VirtualAllocEx(ProcessHandle, IntPtr.Zero, (UIntPtr)injectingFile.Length, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
            if (WriteAddress == IntPtr.Zero)
            {
                return InjectionStatusCode.COULDNT_WRITE_TO_PROCESS;
            }
            bool worked = Utils32.WriteBytesToProcess32(ProcessHandle, WriteAddress, injectingFile);
            if (!worked)
            {
                return InjectionStatusCode.COULDNT_WRITE_TO_PROCESS;
            }
            uint RemoteHeapAlloc;
            uint RemoteHeapFree;
            uint RemoteGetProcessHeap;
            uint RemoteLoadLibraryA;
            uint RemoteGetProcAddress;
            try
            {
                uint RemoteKernel32 = Utils32.GetRemoteModuleHandle32Bit(ProcessHandle, "kernel32.dll");
                RemoteHeapAlloc = Utils32.GetRemoteProcAddress32Bit(ProcessHandle, RemoteKernel32, "HeapAlloc");
                RemoteHeapFree = Utils32.GetRemoteProcAddress32Bit(ProcessHandle, RemoteKernel32, "HeapFree");
                RemoteGetProcessHeap = Utils32.GetRemoteProcAddress32Bit(ProcessHandle, RemoteKernel32, "GetProcessHeap");
                RemoteLoadLibraryA = Utils32.GetRemoteProcAddress32Bit(ProcessHandle, RemoteKernel32, "LoadLibraryA");
                RemoteGetProcAddress = Utils32.GetRemoteProcAddress32Bit(ProcessHandle, RemoteKernel32, "GetProcAddress");
            }
            catch
            {
                return InjectionStatusCode.COULDNT_GET_REMOTE_PROCEDURE_HANDLE;
            }
            if (RemoteHeapAlloc == 0 || RemoteHeapFree == 0 || RemoteGetProcessHeap == 0 || RemoteLoadLibraryA == 0 || RemoteGetProcAddress == 0)
            {
                return InjectionStatusCode.COULDNT_GET_REMOTE_PROCEDURE_HANDLE;
            }

            uint RemoteFileAddress = (uint)WriteAddress;
            uint RemoteFileLength = (uint)injectingFile.Length;

            List<byte> AssemblyBuilder = new List<byte>();
            AssemblyBuilder.AddRange(BitConverter.GetBytes(RemoteHeapAlloc));
            AssemblyBuilder.AddRange(BitConverter.GetBytes(RemoteHeapFree));
            AssemblyBuilder.AddRange(BitConverter.GetBytes(RemoteGetProcessHeap));
            AssemblyBuilder.AddRange(BitConverter.GetBytes(RemoteLoadLibraryA));
            AssemblyBuilder.AddRange(BitConverter.GetBytes(RemoteGetProcAddress));
            AssemblyBuilder.AddRange(BitConverter.GetBytes(RemoteFileAddress));
            AssemblyBuilder.AddRange(BitConverter.GetBytes(RemoteFileLength));

            AssemblyBuilder.AddRange(BitConverter.GetBytes(((uint)NameSpaceAndClass.Length + 1) * 2));//+1 for null bytes, *2 for unicode length
            AssemblyBuilder.AddRange(Encoding.Unicode.GetBytes(NameSpaceAndClass));
            AssemblyBuilder.AddRange(new byte[] { 0, 0 });//ending nullbyte for the string
            AssemblyBuilder.AddRange(BitConverter.GetBytes(((uint)FunctionName.Length + 1) * 2));//+1 for null bytes, *2 for unicode length
            AssemblyBuilder.AddRange(Encoding.Unicode.GetBytes(FunctionName));
            AssemblyBuilder.AddRange(new byte[] { 0, 0 });//ending nullbyte for the string


            int StartOffset = AssemblyBuilder.Count;
            AssemblyBuilder.AddRange(InjectAssembly32Bit);

            byte[] asm = AssemblyBuilder.ToArray();

            WriteAddress = NativeMethods.VirtualAllocEx(ProcessHandle, IntPtr.Zero, (UIntPtr)asm.Length, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
            if (WriteAddress == IntPtr.Zero)
            {
                return InjectionStatusCode.COULDNT_WRITE_TO_PROCESS;
            }
            worked = Utils32.WriteBytesToProcess32(ProcessHandle, WriteAddress, asm);
            if (!worked)
            {
                return InjectionStatusCode.COULDNT_WRITE_TO_PROCESS;
            }
            IntPtr thread = IntPtr.Zero;
            if (NativeMethods.NtCreateThreadEx(ref thread, THREAD_ALL_ACCESS, IntPtr.Zero, ProcessHandle, WriteAddress + StartOffset, WriteAddress, false, 0, 0, 0, IntPtr.Zero) != 0)
            {
                return InjectionStatusCode.COULDNT_CREATE_REMOTE_THREAD;
            }

            IntPtr TimeOut = Marshal.AllocHGlobal(sizeof(long));
            Marshal.WriteInt64(TimeOut, -(MaxWaitTime * 1000000) / 100);//convert the milliconds to 100-nanosecond intervals, (negative to indicate relative)

            if (NativeMethods.NtWaitForSingleObject(thread, false, TimeOut) != 0)
            {
                Marshal.FreeHGlobal(TimeOut);
                return InjectionStatusCode.EXCEEDED_TIMEOUT;
            }
            Marshal.FreeHGlobal(TimeOut);
            uint HandleStructSize = (uint)Marshal.SizeOf(typeof(InternalStructs.THREAD_BASIC_INFORMATION));
            IntPtr pInfo = Marshal.AllocHGlobal((int)HandleStructSize);
            if (NativeMethods.NtQueryInformationThread(thread, InternalStructs.THREADINFOCLASS.ThreadBasicInformation, pInfo, HandleStructSize, out uint _) != 0)
            {
                Marshal.FreeHGlobal(pInfo);
                return InjectionStatusCode.COULDNT_GET_EXITCODE;
            }

            InternalStructs.THREAD_BASIC_INFORMATION basicInfo = (InternalStructs.THREAD_BASIC_INFORMATION)Marshal.PtrToStructure(pInfo, typeof(InternalStructs.THREAD_BASIC_INFORMATION));
            Marshal.FreeHGlobal(pInfo);
            if (basicInfo.ExitStatus == uint.MaxValue)
            {
                return InjectionStatusCode.SHELLCODE_RETURNED_BAD_RESULT;
            }
            return InjectionStatusCode.SUCCESS;
        }


        private static InjectionStatusCode Inject64(IntPtr ProcessHandle, byte[] injectingFile, string NameSpace, string Class, string FunctionName, uint MaxWaitTime = 1000)
        {
            uint MEM_COMMIT = 0x00001000;
            uint MEM_RESERVE = 0x00002000;
            uint PAGE_EXECUTE_READWRITE = 0x40;
            uint PAGE_READWRITE = 0x04;
            uint THREAD_ALL_ACCESS = 0x1FFFFF;
            string NameSpaceAndClass = NameSpace + "." + Class;

            IntPtr WriteAddress = NativeMethods.VirtualAllocEx(ProcessHandle, IntPtr.Zero, (UIntPtr)injectingFile.Length, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
            if (WriteAddress == IntPtr.Zero)
            {
                return InjectionStatusCode.COULDNT_WRITE_TO_PROCESS;
            }
            bool worked = Utils64.WriteBytesToProcess64(ProcessHandle, (ulong)WriteAddress, injectingFile);
            if (!worked)
            {
                return InjectionStatusCode.COULDNT_WRITE_TO_PROCESS;
            }
            ulong RemoteHeapAlloc;
            ulong RemoteHeapFree;
            ulong RemoteGetProcessHeap;
            ulong RemoteLoadLibraryA;
            ulong RemoteGetProcAddress;
            try
            {
                ulong RemoteKernel32 = Utils64.GetRemoteModuleHandle64Bit(ProcessHandle, "kernel32.dll");
                RemoteHeapAlloc = Utils64.GetRemoteProcAddress64Bit(ProcessHandle, RemoteKernel32, "HeapAlloc");
                RemoteHeapFree = Utils64.GetRemoteProcAddress64Bit(ProcessHandle, RemoteKernel32, "HeapFree");
                RemoteGetProcessHeap = Utils64.GetRemoteProcAddress64Bit(ProcessHandle, RemoteKernel32, "GetProcessHeap");
                RemoteLoadLibraryA = Utils64.GetRemoteProcAddress64Bit(ProcessHandle, RemoteKernel32, "LoadLibraryA");
                RemoteGetProcAddress = Utils64.GetRemoteProcAddress64Bit(ProcessHandle, RemoteKernel32, "GetProcAddress");
            }
            catch
            {
                return InjectionStatusCode.COULDNT_GET_REMOTE_PROCEDURE_HANDLE;
            }
            if (RemoteHeapAlloc == 0 || RemoteHeapFree == 0 || RemoteGetProcessHeap == 0 || RemoteLoadLibraryA == 0 || RemoteGetProcAddress == 0)
            {
                return InjectionStatusCode.COULDNT_GET_REMOTE_PROCEDURE_HANDLE;
            }
            ulong RemoteFileAddress = (ulong)WriteAddress;
            ulong RemoteFileLength = (ulong)injectingFile.Length;


            List<byte> AssemblyBuilder = new List<byte>();
            AssemblyBuilder.AddRange(BitConverter.GetBytes(RemoteHeapAlloc));
            AssemblyBuilder.AddRange(BitConverter.GetBytes(RemoteHeapFree));
            AssemblyBuilder.AddRange(BitConverter.GetBytes(RemoteGetProcessHeap));
            AssemblyBuilder.AddRange(BitConverter.GetBytes(RemoteLoadLibraryA));
            AssemblyBuilder.AddRange(BitConverter.GetBytes(RemoteGetProcAddress));
            AssemblyBuilder.AddRange(BitConverter.GetBytes(RemoteFileAddress));
            AssemblyBuilder.AddRange(BitConverter.GetBytes(RemoteFileLength));

            AssemblyBuilder.AddRange(BitConverter.GetBytes(((ulong)NameSpaceAndClass.Length + 1) * 2));//+1 for null bytes, *2 for unicode length
            AssemblyBuilder.AddRange(Encoding.Unicode.GetBytes(NameSpaceAndClass));
            AssemblyBuilder.AddRange(new byte[] { 0, 0 });//ending nullbyte for the string
            AssemblyBuilder.AddRange(BitConverter.GetBytes(((ulong)FunctionName.Length + 1) * 2));//+1 for null bytes, *2 for unicode length
            AssemblyBuilder.AddRange(Encoding.Unicode.GetBytes(FunctionName));
            AssemblyBuilder.AddRange(new byte[] { 0, 0 });//ending nullbyte for the string


            int StartOffset = AssemblyBuilder.Count;
            AssemblyBuilder.AddRange(InjectAssembly64Bit);
            byte[] asm = AssemblyBuilder.ToArray();
            WriteAddress = NativeMethods.VirtualAllocEx(ProcessHandle, IntPtr.Zero, (UIntPtr)asm.Length, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
            if (WriteAddress == IntPtr.Zero)
            {
                return InjectionStatusCode.COULDNT_WRITE_TO_PROCESS;
            }
            worked = Utils64.WriteBytesToProcess64(ProcessHandle, (ulong)WriteAddress, asm);
            if (!worked)
            {
                return InjectionStatusCode.COULDNT_WRITE_TO_PROCESS;
            }
            uint excode;
            if (Environment.Is64BitProcess)
            {
                IntPtr thread = IntPtr.Zero;
                if (NativeMethods.NtCreateThreadEx(ref thread, THREAD_ALL_ACCESS, IntPtr.Zero, ProcessHandle, WriteAddress + StartOffset, WriteAddress, false, 0, 0, 0, IntPtr.Zero) != 0)
                {
                    return InjectionStatusCode.COULDNT_CREATE_REMOTE_THREAD;
                }

                IntPtr TimeOut = Marshal.AllocHGlobal(sizeof(long));
                Marshal.WriteInt64(TimeOut, -(MaxWaitTime * 1000000) / 100);//convert the milliconds to 100-nanosecond intervals, (negative to indicate relative)

                if (NativeMethods.NtWaitForSingleObject(thread, false, TimeOut) != 0)
                {
                    Marshal.FreeHGlobal(TimeOut);
                    return InjectionStatusCode.EXCEEDED_TIMEOUT;
                }
                Marshal.FreeHGlobal(TimeOut);

                uint HandleStructSize = (uint)Marshal.SizeOf(typeof(InternalStructs.THREAD_BASIC_INFORMATION));
                IntPtr pInfo = Marshal.AllocHGlobal((int)HandleStructSize);
                if (NativeMethods.NtQueryInformationThread(thread, InternalStructs.THREADINFOCLASS.ThreadBasicInformation, pInfo, HandleStructSize, out uint _) != 0)
                {
                    Marshal.FreeHGlobal(pInfo);
                    return InjectionStatusCode.COULDNT_GET_EXITCODE;
                }

                //InternalStructs.THREAD_BASIC_INFORMATION basicInfo = Marshal.PtrToStructure<InternalStructs.THREAD_BASIC_INFORMATION>(pInfo);
                InternalStructs.THREAD_BASIC_INFORMATION basicInfo = (InternalStructs.THREAD_BASIC_INFORMATION)Marshal.PtrToStructure(pInfo, typeof(InternalStructs.THREAD_BASIC_INFORMATION));
                Marshal.FreeHGlobal(pInfo);
                excode = basicInfo.ExitStatus;
            }
            else
            {
                if (!HeavensGate.operational)
                {
                    return InjectionStatusCode.HEAVENSGATE_NON_OPERATIONAL;
                }
                ulong ntdll64Handle = HeavensGate.GetModuleHandle64("ntdll.dll");
                if (ntdll64Handle == 0)
                {
                    return InjectionStatusCode.COULDNT_GET_NTDLL_FROM_HEAVENSGATE;
                }
                ulong NtCreateThreadExHandle = HeavensGate.GetProcAddress64(ntdll64Handle, "NtCreateThreadEx");
                ulong NtWaitForSingleObjectHandle = HeavensGate.GetProcAddress64(ntdll64Handle, "NtWaitForSingleObject");
                ulong NtQueryInformationThreadHandle = HeavensGate.GetProcAddress64(ntdll64Handle, "NtQueryInformationThread");

                IntPtr pThread = Marshal.AllocHGlobal(sizeof(ulong));
                if (HeavensGate.Execute64(NtCreateThreadExHandle, (ulong)pThread, THREAD_ALL_ACCESS, 0, (ulong)ProcessHandle, (ulong)(WriteAddress + StartOffset), (ulong)WriteAddress, 0, 0, 0, 0, 0) != 0)
                {
                    Marshal.FreeHGlobal(pThread);
                    return InjectionStatusCode.COULDNT_CREATE_REMOTE_THREAD;
                }

                //ulong thread = Marshal.PtrToStructure<InternalStructs.ULONGRESULT>(pThread).Value;
                ulong thread = ((InternalStructs.ULONGRESULT)Marshal.PtrToStructure(pThread, typeof(InternalStructs.ULONGRESULT))).Value;
                Marshal.FreeHGlobal(pThread);

                IntPtr TimeOut = Marshal.AllocHGlobal(sizeof(long));
                Marshal.WriteInt64(TimeOut, -(MaxWaitTime * 1000000) / 100);//convert the milliconds to 100-nanosecond intervals, (negative to indicate relative)

                if (HeavensGate.Execute64(NtWaitForSingleObjectHandle, thread, 0, (ulong)TimeOut) != 0)
                {
                    Marshal.FreeHGlobal(TimeOut);
                    return InjectionStatusCode.EXCEEDED_TIMEOUT;
                }
                Marshal.FreeHGlobal(TimeOut);

                uint HandleStructSize = (uint)Marshal.SizeOf(typeof(InternalStructs64.THREAD_BASIC_INFORMATION64));
                IntPtr pInfo = Marshal.AllocHGlobal((int)HandleStructSize);
                if (HeavensGate.Execute64(NtQueryInformationThreadHandle, thread, (uint)InternalStructs.THREADINFOCLASS.ThreadBasicInformation, (ulong)pInfo, HandleStructSize, 0) != 0)
                {
                    Marshal.FreeHGlobal(pInfo);
                    return InjectionStatusCode.COULDNT_GET_EXITCODE;
                }

                //InternalStructs64.THREAD_BASIC_INFORMATION64 basicInfo = Marshal.PtrToStructure<InternalStructs64.THREAD_BASIC_INFORMATION64>(pInfo);
                InternalStructs64.THREAD_BASIC_INFORMATION64 basicInfo = (InternalStructs64.THREAD_BASIC_INFORMATION64)Marshal.PtrToStructure(pInfo, typeof(InternalStructs64.THREAD_BASIC_INFORMATION64));
                Marshal.FreeHGlobal(pInfo);
                excode = basicInfo.ExitStatus;
            }
            if (excode == uint.MaxValue)
            {
                return InjectionStatusCode.SHELLCODE_RETURNED_BAD_RESULT;
            }
            return InjectionStatusCode.SUCCESS;

        }

    }
}
