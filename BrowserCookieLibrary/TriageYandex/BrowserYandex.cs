using BrowserLibrary;
using BrowserCookieLibrary.TriageChrome;
using System;
using System.IO;
using System.Net;
using System.Collections.Generic;

namespace BrowserCookieLibrary.TriageYandex
{
    public partial class TriageYandexCookies
    {

        public static CookieContainer BrowserYandex(Dictionary<string, string[]> websiteCookies)
        {
            CookieContainer cookies = null;
            var username = Environment.UserName;
            string userLocal = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            string userDataPath = @"Yandex\YandexBrowser\User Data";
            string browserUserDataPath = Path.Combine(userLocal, userDataPath);
            string state_file = Path.Combine(browserUserDataPath, "Local State");

            if (!File.Exists(state_file))
            {
                Console.WriteLine($"[!] [{username}] Yandex 'Local State' Not Found!");
                return cookies;
            }

            ChromeDecryptor decryptor = new ChromeDecryptor(browserUserDataPath, null);

            var profiles = TriageChromeCookies.GetLastUsedProfiles(state_file);

            //Console.WriteLine($"[*] {username} StateKey: {Convert.ToBase64String(stateKey)}");
            Console.WriteLine($"[*] {username} last_used: {profiles}");

            string profilesPath = Path.Combine(browserUserDataPath, profiles);

            var tmpPath = string.Empty;
            var sourceCookiesFilePath = Path.Combine(profilesPath, "Cookies");
            var sourceNetworkCookiesFilePath = Path.Combine(profilesPath, "Network\\Cookies");
            if (File.Exists(sourceCookiesFilePath))
                tmpPath = sourceCookiesFilePath;
            else if (File.Exists(sourceNetworkCookiesFilePath))
                tmpPath = sourceNetworkCookiesFilePath;
            else
                Console.WriteLine("[!] Yandex 'Login Cookies' Not Found!");

            Console.WriteLine("[*] Assembling Cookie.");
            cookies = TriageChromeCookies.TriageCookies(decryptor, Utils.ForceReadFile(tmpPath), websiteCookies);
            return cookies;
        }
    }
}
