using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using static BrowserCookieLibrary.Win32Api;

namespace BrowserCookieLibrary.MemoryCookie
{
#if x64

    class TokenHelper
    {
        const int TOKEN_QUERY = 0x0008;

        [StructLayout(LayoutKind.Sequential)]
        public struct TOKEN_USER
        {
            public _SID_AND_ATTRIBUTES User;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct _SID_AND_ATTRIBUTES
        {
            public IntPtr Sid;
            public int Attributes;
        }

        const int TokenUser = 1;
        const int MAX_NAME = 256;

        public static bool GetTokenUser(IntPtr hProcess)
        {
            IntPtr hToken;
            if (!OpenProcessToken(hProcess, TOKEN_QUERY, out hToken))
            {
                Console.WriteLine("GetTokenUser OpenProcessToken failed!");
                return false;
            }

            int dwSize = 0;
            GetTokenInformation(hToken, TokenUser, IntPtr.Zero, 0, out dwSize);

            if (dwSize == 0)
            {
                Console.WriteLine("GetTokenUser GetTokenInformation querying buffer size failed!");
                return false;
            }

            IntPtr tokenInfo = Marshal.AllocHGlobal(dwSize);

            try
            {
                if (!GetTokenInformation(hToken, TokenUser, tokenInfo, dwSize, out dwSize))
                {
                    Console.WriteLine("GetTokenUser GetTokenInformation failed!");
                    return false;
                }

                TOKEN_USER tokenUser = (TOKEN_USER)Marshal.PtrToStructure(tokenInfo, typeof(TOKEN_USER));

                uint dwMaxUserName = MAX_NAME;
                uint dwMaxDomainName = MAX_NAME;
                StringBuilder UserName = new StringBuilder((int)dwMaxUserName);
                StringBuilder DomainName = new StringBuilder((int)dwMaxDomainName);

                int sidType;
                byte[] sidBytes = new byte[Marshal.ReadInt32(tokenUser.User.Sid)];
                Marshal.Copy(tokenUser.User.Sid, sidBytes, 0, sidBytes.Length);

                if (!LookupAccountSid(null, sidBytes, UserName, ref dwMaxUserName, DomainName, ref dwMaxDomainName, out sidType))
                {
                    Console.WriteLine("GetTokenUser LookupAccountSid failed!");
                    return false;
                }

                Console.WriteLine($"{DomainName}\\{UserName}");
                return true;
            }
            finally
            {
                Marshal.FreeHGlobal(tokenInfo);
                CloseHandle(hToken);
            }
        }

        [DllImport("kernel32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        static extern bool CloseHandle(IntPtr hObject);
    }
#endif
}
