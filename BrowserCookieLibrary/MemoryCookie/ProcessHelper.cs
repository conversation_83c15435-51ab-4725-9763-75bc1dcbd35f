using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using static BrowserCookieLibrary.Win32Api;

namespace BrowserCookieLibrary.MemoryCookie
{
#if x64

    class ProcessHelper
    {

        const int PROCESS_QUERY_INFORMATION = 0x0400;
        const int PROCESS_VM_READ = 0x0010;
        const int LIST_MODULES_ALL = 0x03;
        const uint TH32CS_SNAPPROCESS = 0x00000002;
        const string FLAGS = "--utility-sub-type=network.mojom.NetworkService";

        private static bool ReadProcessMemory(IntPtr hProcess, IntPtr baseAddress, out IntPtr buffer)
        {
            buffer = IntPtr.Zero;
            byte[] tempBuffer = new byte[IntPtr.Size];
            if (!Win32Api.ReadProcessMemory(hProcess, baseAddress, tempBuffer, (uint)tempBuffer.Length, out _))
                return false;

            buffer = (IntPtr)BitConverter.ToInt64(tempBuffer, 0);
            return true;
        }

        private static bool ReadProcessMemory(IntPtr hProcess, IntPtr baseAddress, out Win32Api.UNICODE_STRING buffer)
        {
            buffer = new Win32Api.UNICODE_STRING();
            byte[] tempBuffer = new byte[Marshal.SizeOf(typeof(Win32Api.UNICODE_STRING))];
            if (!Win32Api.ReadProcessMemory(hProcess, baseAddress, tempBuffer, (uint)tempBuffer.Length, out _))
                return false;

            GCHandle handle = GCHandle.Alloc(tempBuffer, GCHandleType.Pinned);
            buffer = (Win32Api.UNICODE_STRING)Marshal.PtrToStructure(handle.AddrOfPinnedObject(), typeof(UNICODE_STRING));
            handle.Free();
            return true;
        }

        public static string GetCommandLine(IntPtr processHandle)
        {
            Win32Api.PROCESS_BASIC_INFORMATION pbi = new Win32Api.PROCESS_BASIC_INFORMATION();
            uint returnLength;
            int status = Win32Api.NtQueryInformationProcess(processHandle, 0, ref pbi, (uint)Marshal.SizeOf(pbi), out returnLength);

            if (status != 0)
            {
                Console.WriteLine("NtQueryInformationProcess failed");
                return null;
            }

            IntPtr pebAddress = pbi.PebBaseAddress;
            IntPtr rtlUserProcParamsAddress = IntPtr.Zero;

            // Read the address of ProcessParameters from the PEB
            if (!ReadProcessMemory(processHandle, pebAddress + 0x20, out rtlUserProcParamsAddress))
            {
                Console.WriteLine("Could not read ProcessParameters address");
                return null;
            }

            // Read the command line UNICODE_STRING structure
            Win32Api.UNICODE_STRING commandLine = new Win32Api.UNICODE_STRING();
            if (!ReadProcessMemory(processHandle, rtlUserProcParamsAddress + 0x70, out commandLine))
            {
                Console.WriteLine("Could not read CommandLine UNICODE_STRING");
                return null;
            }

            // Allocate buffer and read the command line
            byte[] buffer = new byte[commandLine.MaximumLength];
            if (!Win32Api.ReadProcessMemory(processHandle, commandLine.Buffer, buffer, commandLine.MaximumLength, out _))
            {
                Console.WriteLine("Could not read command line string");
                return null;
            }

            return System.Text.Encoding.Unicode.GetString(buffer);
        }

        public static bool FindCorrectProcessPID(string processName, out uint pid, out IntPtr hProcess)
        {
            IntPtr hProcessSnap = Win32Api.CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
            if (hProcessSnap == IntPtr.Zero)
            {
                Console.WriteLine("CreateToolhelp32Snapshot failed");
                pid = 0;
                hProcess = IntPtr.Zero;
                return false;
            }

            Win32Api.PROCESSENTRY32 pe32 = new PROCESSENTRY32();
            pe32.dwSize = (uint)Marshal.SizeOf(typeof(PROCESSENTRY32));

            if (!Process32First(hProcessSnap, ref pe32))
            {
                Console.WriteLine("Process32First failed");
                CloseHandle(hProcessSnap);
                pid = 0;
                hProcess = IntPtr.Zero;
                return false;
            }

            do
            {
                if (pe32.szExeFile.Equals(processName, StringComparison.OrdinalIgnoreCase))
                {
                    IntPtr hHandle = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, false, pe32.th32ProcessID);
                    if (hHandle != IntPtr.Zero)
                    {
                        // Simplified: You will need to implement ReadRemoteProcessPEB and ReadPEBProcessParameters
                        // to check command line arguments like in the original C++ code
                        string commandLine = GetCommandLine(hHandle);

                        if (!string.IsNullOrEmpty(commandLine) && commandLine.Contains(FLAGS))
                        {
                            pid = pe32.th32ProcessID;
                            hProcess = hHandle;
                            CloseHandle(hProcessSnap);
                            return true;
                        }

                        CloseHandle(hHandle);
                    }
                }

            } while (Process32Next(hProcessSnap, ref pe32));

            CloseHandle(hProcessSnap);
            pid = 0;
            hProcess = IntPtr.Zero;
            return false;
        }

        public static bool IsWow64(IntPtr processHandle)
        {
            try
            {
                if (!IsWow64Process(processHandle, out bool isWow64))
                {
                    Console.WriteLine("IsWow64Process failed for browser process");
                    return true;
                }

                if (isWow64)
                {
                    return true;
                }

                return false;
            }
            finally
            {
                // Ensure the handle is closed to prevent resource leaks
                //CloseHandle(processHandle);
            }
        }

        public static bool GetRemoteModuleBaseAddress(IntPtr processHandle, string moduleName, out IntPtr baseAddress, out uint moduleSize)
        {
            baseAddress = IntPtr.Zero;
            moduleSize = 0;

            const int MaxModules = 1024;
            IntPtr[] modules = new IntPtr[MaxModules];
            uint cbNeeded;

            GCHandle moduleHandle = GCHandle.Alloc(modules, GCHandleType.Pinned);
            try
            {
                if (!EnumProcessModulesEx(processHandle, moduleHandle.AddrOfPinnedObject(), (uint)(IntPtr.Size * MaxModules), out cbNeeded, 0x03)) // LIST_MODULES_ALL = 0x03
                {
                    Console.WriteLine("EnumProcessModulesEx failed");
                    return false;
                }

                int moduleCount = (int)(cbNeeded / (uint)IntPtr.Size);
                StringBuilder moduleNameBuilder = new StringBuilder(260); // MAX_PATH

                for (int i = 0; i < moduleCount; ++i)
                {
                    if (GetModuleBaseName(processHandle, modules[i], moduleNameBuilder, (uint)moduleNameBuilder.Capacity) == 0)
                    {
                        Console.WriteLine("GetModuleBaseName failed");
                        continue;
                    }

                    if (string.Equals(moduleNameBuilder.ToString(), moduleName, StringComparison.OrdinalIgnoreCase))
                    {
                        if (GetModuleInformation(processHandle, modules[i], out MODULEINFO moduleInfo, (uint)Marshal.SizeOf(typeof(MODULEINFO))))
                        {
                            baseAddress = moduleInfo.lpBaseOfDll;
                            moduleSize = moduleInfo.SizeOfImage;
                            return true;
                        }
                        else
                        {
                            Console.WriteLine("GetModuleInformation failed");
                            return false;
                        }
                    }
                }
            }
            finally
            {
                moduleHandle.Free();
            }

            return false;
        }
    }
#endif
}
