using System;
using System.Runtime.InteropServices;
using static BrowserCookieLibrary.Win32Api;

namespace BrowserCookieLibrary.MemoryCookie
{

    class VersionHelper
    {
        //[DllImport("version.dll", CharSet = CharSet.Auto)]
        //public static extern int GetFileVersionInfoSize(string lptstrFilename, out int lpdwHandle);

        //[DllImport("version.dll", CharSet = CharSet.Auto)]
        //public static extern bool GetFileVersionInfo(string lptstrFilename, int dwHandle, int dwLen, byte[] lpData);

        //[DllImport("version.dll", CharSet = CharSet.Auto)]
        //public static extern bool VerQueryValue(byte[] pBlock, string lpSubBlock, out IntPtr lplpBuffer, out uint puLen);


        //[StructLayout(LayoutKind.Sequential)]
        //private struct VS_FIXEDFILEINFO
        //{
        //    public uint dwSignature;
        //    public uint dwStrucVersion;
        //    public uint dwFileVersionMS;
        //    public uint dwFileVersionLS;
        //    public uint dwProductVersionMS;
        //    public uint dwProductVersionLS;
        //    public uint dwFileFlagsMask;
        //    public uint dwFileFlags;
        //    public uint dwFileOS;
        //    public uint dwFileType;
        //    public uint dwFileSubtype;
        //    public uint dwFileDateMS;
        //    public uint dwFileDateLS;
        //}

        //public static bool GetChromeVersion(out ushort chromeMajorVersion)
        //{
        //    chromeMajorVersion = 0;
        //    string fileName = @"C:\Program Files\Google\Chrome\Application\chrome.exe";

        //    int handle;
        //    int size = GetFileVersionInfoSize(fileName, out handle);
        //    if (size == 0)
        //    {
        //        Console.WriteLine("GetFileVersionInfoSize failed");
        //        return false;
        //    }

        //    byte[] buffer = new byte[size];
        //    if (!GetFileVersionInfo(fileName, 0, size, buffer))
        //    {
        //        Console.WriteLine("GetFileVersionInfo failed");
        //        return false;
        //    }

        //    IntPtr fixedFileInfoPtr;
        //    uint len;
        //    if (!VerQueryValue(buffer, @"\", out fixedFileInfoPtr, out len) || len == 0)
        //    {
        //        Console.WriteLine("VerQueryValue failed");
        //        return false;
        //    }

        //    VS_FIXEDFILEINFO fileInfo = (VS_FIXEDFILEINFO)Marshal.PtrToStructure(fixedFileInfoPtr, typeof(VS_FIXEDFILEINFO));
        //    chromeMajorVersion = (ushort)(fileInfo.dwProductVersionMS >> 16);

        //    Console.WriteLine($"[*] Chrome Version: {chromeMajorVersion}");
        //    return true;
        //}

        //public struct SupportedVersion
        //{
        //    public ushort MajorVersion;
        //    public byte[] Pattern;
        //}

        //public static SupportedVersion[] GetSupportedVersions()
        //{
        //    return new SupportedVersion[]
        //    {
        //    new SupportedVersion
        //    {
        //        MajorVersion = 120,
        //        Pattern = new byte[]
        //        {
        //            0x56, 0x57, 0x48, 0x83, 0xEC, 0x28, 0x89, 0xD7,
        //            0x48, 0x89, 0xCE, 0xE8, 0xC0, 0xD5, 0xFF, 0xFF,
        //            0x85, 0xFF, 0x74, 0x08, 0x48, 0x89, 0xF1, 0xE8,
        //            0xE4, 0xB8, 0x66, 0xFD, 0x48, 0x89, 0xF0, 0x48
        //        }
        //    }
        //    };
        //}

        //public static bool GetSearchPattern(out byte[] pattern)
        //{
        //    pattern = new byte[144];
        //    if (!GetChromeVersion(out ushort chromeMajorVersion))
        //    {
        //        Console.WriteLine("[-] Failed to determine Chrome version");
        //        return false;
        //    }

        //    foreach (var supportedVersion in GetSupportedVersions())
        //    {
        //        if (supportedVersion.MajorVersion == chromeMajorVersion)
        //        {
        //            Array.Copy(supportedVersion.Pattern, pattern, supportedVersion.Pattern.Length);
        //            return true;
        //        }
        //    }

        //    Console.WriteLine("[-] This version of Chrome is not supported!");
        //    return false;
        //}
    }
}
