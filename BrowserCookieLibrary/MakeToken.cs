using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;


namespace BrowserCookieLibrary
{
    public class MakeToken
    {
        public static bool ImpersonateProcessToken(int pid)
        {
            IntPtr hProcess = Win32Api.OpenProcess(Win32Api.PROCESS_ACCESS_FLAGS.PROCESS_QUERY_INFORMATION, true, pid);
            if (hProcess == IntPtr.Zero) return false;
            IntPtr hToken;
            if (!Win32Api.OpenProcessToken(hProcess, Win32Api.TOKEN_IMPERSONATE | Win32Api.TOKEN_DUPLICATE, out hToken)) return false;
            IntPtr DuplicatedToken = new IntPtr();
            if (!Win32Api.DuplicateToken(hToken, 2, ref DuplicatedToken)) return false;
            if (!Win32Api.SetThreadToken(IntPtr.Zero, DuplicatedToken)) return false;
            return true;

            //IntPtr hProcess = Native.OpenProcess(Native.PROCESS_ACCESS_FLAGS.PROCESS_QUERY_INFORMATION, true, pid);
            //if (hProcess == IntPtr.Zero) return false;
            //IntPtr hToken;
            //if (!Native.OpenProcessToken(hProcess, 0x00000002 | 0x00000004, out hToken)) return false;
            //IntPtr DuplicatedToken = new IntPtr();
            //if (!Native.DuplicateToken(hToken, 2, ref DuplicatedToken)) return false;
            //if (!Native.SetThreadToken(IntPtr.Zero, DuplicatedToken)) return false;
            //return true;

        }
    }
}
