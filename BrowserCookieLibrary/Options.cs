using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace BrowserCookieLibrary
{
    public class Options
    {
        public enum Browser
        {
            Chrome,
            Firefox,
            Msedge,
            Yandex
        };

        public static string Helper()
        {
            string banner = @"
SharpBrowserMail chrome/edge/yandex 20220101-20220401 <pid>
";
            return banner;
        }

        public static void MergeDictionaries(Dictionary<string, string> target, Dictionary<string, string> source)
        {
            foreach (var kvp in source)
            {
                if (!target.ContainsKey(kvp.Key))
                {
                    target[kvp.Key] = kvp.Value;
                }
            }
        }
    }
}
