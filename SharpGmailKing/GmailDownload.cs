using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Security;
using System.Runtime.Serialization.Json;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.RegularExpressions;
using System.Web.Script.Serialization;

namespace SharpGmailKing
{
    // 自定义类，用于存储请求令牌信息
    public class RequestToken
    {
        public string versionToken { get; set; }
        public string xsrfToken { get; set; }
        public string globals { get; set; }
        public string gmail { get; set; }
    }

    class GmailDownload
    {
        public static void CopyStream(Stream source, Stream destination)
        {
            byte[] buffer = new byte[8192]; // 定义缓冲区大小
            int bytesRead;
            while ((bytesRead = source.Read(buffer, 0, buffer.Length)) > 0)
            {
                destination.Write(buffer, 0, bytesRead);
            }
        }

        /// <summary>
        /// 根据下载地址，获取邮件内容
        /// </summary>
        public static void DownloadEml(string url, string username, string filepath)
        {
            string emlFileName = $"[{filepath}].eml";
            string saveFolder = Path.Combine("MailStore", username);
            Directory.CreateDirectory(saveFolder);

            string savePath = Path.Combine(saveFolder, emlFileName);
            if (File.Exists(savePath)) return;

            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = "GET";
            request.Timeout = 10 * 60 * 1000;
            request.ReadWriteTimeout = 10 * 60 * 1000;

            try
            {
                using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
                {
                    if (response.StatusCode == HttpStatusCode.OK)
                    {

                        using (Stream responseStream = response.GetResponseStream())
                        using (FileStream fileStream = new FileStream(savePath, FileMode.Create))
                        {
                            CopyStream(responseStream, fileStream);
                            //responseStream.CopyTo(fileStream);
                        }
                        Console.WriteLine("  [>] " + savePath);
                    }
                    else
                    {
                        Console.WriteLine("[!] Download failed, status code: " + response.StatusCode);
                    }
                }
            }
            catch (WebException e)
            {
                Console.WriteLine("[!] Error occurred while downloading: " + e.Message);
            }
        }

        /// <summary>
        /// 获取指定 id 的邮件下载地址
        /// </summary>
        public static string GetDownloadMailUrl(CookieContainer cookies, string permmsgid)
        {
            //string url = $"https://mail.google.com/mail/u/0/?ik=xxxxxx&view=att&permmsgid={permmsgid}&disp=comp&safe=1";
            string url = $"https://mail.google.com/mail/u/0/?ik=rdsgsaa&view=att&permmsgid={permmsgid}&disp=comp&safe=1";
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = "GET";
            request.AllowAutoRedirect = false;
            request.CookieContainer = cookies;

            try
            {
                using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
                {
                    if (response.StatusCode == HttpStatusCode.Found)
                    {
                        string locationHeader = response.Headers["Location"];
                        return locationHeader;
                    }
                    else
                    {
                        Console.WriteLine("[!] Failed to retrieve data: " + response.StatusCode);
                    }
                }
            }
            catch (WebException e)
            {
                Console.WriteLine("[!] Request error: " + e.Message);
            }
            return null;
        }

        static string SerializeToJson(object obj)
        {
            using (var memoryStream = new MemoryStream())
            {
                var serializer = new DataContractJsonSerializer(obj.GetType());
                serializer.WriteObject(memoryStream, obj);
                return Encoding.UTF8.GetString(memoryStream.ToArray());
            }
        }

        /// <summary>
        /// 处理邮件 id 和时间戳
        /// </summary>
        //static Dictionary<string, string> FindKeys(object[] array)
        //{
        //    var msgID = new Dictionary<string, string>();

        //    for (int i = 0; i < array.Length; i++)
        //    {
        //        try
        //        {
        //            var item = array[i];

        //            if (item is object[] innerArray)
        //            {
        //                var innerDict = FindKeys(innerArray);
        //                foreach (var kvp in innerDict)
        //                {
        //                    msgID[kvp.Key] = kvp.Value;
        //                }
        //            }
        //            else if (item != null)
        //            {
        //                if (item.ToString().ToLower().Contains("msg-f"))
        //                {
        //                    if (i + 5 < array.Length)
        //                    {
        //                        var timestampStr = array[i + 6];
        //                        if (timestampStr == null) continue;
        //                        // 将字符串转换为 long
        //                        long timestamp;
        //                        if (long.TryParse(timestampStr.ToString(), out timestamp))
        //                        {
        //                            // Unix 时间戳开始的日期
        //                            DateTime epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);

        //                            // 将时间戳从毫秒转换为日期
        //                            DateTime dateTime = epoch.AddMilliseconds(timestamp).ToLocalTime();
        //                            msgID[item.ToString()] = dateTime.ToString("yyyyMMdd-HHmmss"); // 使用 null 合并运算符
        //                        }
        //                        else
        //                        {
        //                            Console.WriteLine("Invalid timestamp format.");
        //                        }
        //                    }
        //                }
        //            }
        //        }
        //        catch
        //        {
        //        }
        //    }

        //    return msgID;
        //}

        public class MsgID
        {
            public string Id { get; set; }
            public string Timestamp { get; set; }

            public MsgID(string id, string timestamp)
            {
                Id = id;
                Timestamp = timestamp;
            }
        }

        /// <summary>
        /// 处理邮件 id 和时间戳
        /// </summary>
        static List<MsgID> FindKeys(object[] array)
        {
            var msgList = new List<MsgID>();

            for (int i = 0; i < array.Length; i++)
            {
                try
                {
                    var item = array[i];

                    if (item is object[] innerArray)
                    {
                        var innerMsgList = FindKeys(innerArray);
                        msgList.AddRange(innerMsgList);
                    }
                    else if (item != null)
                    {
                        if (item.ToString().ToLower().Contains("msg-f"))
                        {
                            if (i + 6 < array.Length) // 修正索引以匹配时间戳的位置
                            {
                                var timestampStr = array[i + 6];
                                if (timestampStr == null) continue;

                                // 将字符串转换为 long
                                long timestamp;
                                if (long.TryParse(timestampStr.ToString(), out timestamp))
                                {
                                    // Unix 时间戳开始的日期
                                    DateTime epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);

                                    // 将时间戳从毫秒转换为日期
                                    DateTime dateTime = epoch.AddMilliseconds(timestamp).ToLocalTime();
                                    string formattedDateTime = dateTime.ToString("yyyyMMdd-HHmmss");

                                    // 创建 MsgID 对象并添加到列表
                                    msgList.Add(new MsgID(item.ToString(), formattedDateTime));
                                }
                                else
                                {
                                    //Console.WriteLine("Invalid timestamp format.");
                                }
                            }
                        }
                    }
                }
                catch
                {
                    // 处理异常
                }
            }

            return msgList;
        }

        // 创建数据数组的方法
        static object[] CreateDataArray(string date, int index)
        {
            //var data = new object[]
            //{
            //new object[] { 49, 100, null, "((in:^i ((in:^smartlabel_personal) OR (in:^t))) OR (in:^i -in:^smartlabel_promo -in:^smartlabel_social))", new object[] { null, null, null, null, 0 }, "itemlist-ViewType(49)-11", 1, 2000, null, 0, null, null, null, 1, null, null, null, null, 0 },
            //null,
            //new object[] { 0, 5, null, null, 1, 1, 1 }
            //};

            //data = new object[]
            //{
            //    new object[]
            //    {
            //        123,
            //        500,
            //        null,
            //        date,
            //        new object[]
            //        {
            //            null, null, null, null, 0, null, null, null, null, null, null,
            //            1727170184971, 28800000, null, null, null, null, null, null, null,
            //            null, null, null, 0, 0, 0, 0
            //        },
            //        "itemlist-ViewType(123)-7",
            //        1,
            //        2000,
            //        null,
            //        1,
            //        null,
            //        null,
            //        null,
            //        1,
            //        null,
            //        new object[]
            //        {
            //            1, 0, 0, null, null, null, 1,
            //            "13CBD97C-4C1E-41F6-A183-B6C3B1C833DE", null, 1, null, null, 1, null, 1
            //        },
            //        null,
            //        null,
            //        1,
            //        null,
            //        null,
            //        0,
            //        1,
            //        0,
            //        new object[]
            //        {
            //            new object[]
            //            {
            //                new object[] { 2, 50 }
            //            }
            //        },
            //        0,
            //        0
            //    },
            //    null,
            //    new object[] { 0, 5, null, null, 1, 1, 1 }
            //};
            return new object[]
            {
                new object[]
                {
                    123,
                    500,
                    null,
                    date,
                    new object[] { null, null, null, null, 0 },
                    "itemlist-ViewType(123)-7",
                    1,
                    2000,
                    null,
                    index,
                    null,
                    null,
                    null,
                    1,
                    null,
                    new object[]
                    {
                        1, 0, 0, null, null, null, 1,
                        "", null, 1, null, null, 1, null, 1
                    },
                    null,
                    null,
                    1,
                    null,
                    null,
                    0,
                    1,
                    0,
                    new object[]
                    {
                        new object[]
                        {
                            new object[] { 1, 230 }
                        }
                    },
                    0,
                    0
                },
                null,
                new object[] { 0, 5, null, null, 1, 1, 1 }
            };
        }

        /// <summary>
        /// 获取根据搜索条件邮箱列表
        /// </summary>
        public static List<MsgID> GetMailMsgID(string date, CookieContainer cookies, string xsrfToken, string versionToken, string globals)
        {
            var msgList = new List<MsgID>();
            
            int page = 20;
            for (int i = 0; i <= page; i++)
            {
                string url = "https://mail.google.com/sync/u/0/i/bv?hl=en&c=5&rt=r&pt=ji";

                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
                request.CookieContainer = cookies;
                request.Method = "POST";
                request.ContentType = "application/json";

                request.Headers["X-Gmail-Storage-Request"] = "";
                request.Headers["X-Framework-Xsrf-Token"] = xsrfToken;
                request.Headers["Sec-Ch-Ua-Mobile"] = "?0";
                request.Headers["X-Gmail-Btai"] = $"[null,null,[1,1,1,1,1,0,1,1,1,1,1,1,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,1,1,1,1,1,0,1,\"en\",\"\",1,1,24,1,0,1,0,1,1,1,1,1,1,1,1,0,1,1,0,0,1,0,1,1,1,0,1,1,0,1,1,0,1,1,1,0,0,1,1,1,1,100,1,1,0,1,0,1,0,1,0,0,1,1,1,0,0,0,0,0,0],1,\"{versionToken}\",111,25,\"\",11,1,\"\",1,\"1\",1,null,{globals},\"\",\"\",11]";

                var data = CreateDataArray(date, i);
                string jsonData = SerializeToJson(data);
                //jsonData = @"[[123,500,null,""after:2023\/09\/01 before:2024\/09\/02"",[null,null,null,null,0],""itemlist-ViewType(123)-7"",1,2000,null,0,null,null,null,1,null,[1,0,0,null,null,null,1,null,null,1,null,null,1,null,1],null,null,1,null,null,0,1,0,[[[1,230]]],0,0],null,[0,5,null,null,1,1,1]]";
                
                try
                {
                    using (var streamWriter = new StreamWriter(request.GetRequestStream()))
                    {
                        streamWriter.Write(jsonData);
                    }
                    using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
                    {
                        if (response.StatusCode == HttpStatusCode.OK)
                        {
                            using (StreamReader reader = new StreamReader(response.GetResponseStream()))
                            {
                                string responseData = reader.ReadToEnd();

                                var serializer = new JavaScriptSerializer();
                                var result = serializer.Deserialize<object[]>(responseData);
                                var dd = FindKeys(result);
                                msgList.AddRange(dd);
                                if (dd.Count < 230) break;
                            }
                        }
                        else
                        {
                            Console.WriteLine("[!] Failed to retrieve data: " + response.StatusCode);
                        }
                    }
                }
                catch (WebException e)
                {
                    Console.WriteLine("[!] Request error: " + e.Message);
                }
            }

            return msgList;
        }



        static bool cert(object o, X509Certificate x, X509Chain c, SslPolicyErrors s) { return true; }


        /// <summary>
        /// 获取访问 gmail 所必须的值
        /// </summary>
        public static RequestToken GetRequestToken(CookieContainer cookies)
        {
            ServicePointManager.ServerCertificateValidationCallback = new RemoteCertificateValidationCallback(cert);
            ServicePointManager.Expect100Continue = false;
            ServicePointManager.DefaultConnectionLimit = int.MaxValue;
            ServicePointManager.MaxServicePoints = int.MaxValue;


            string versionToken = string.Empty;
            string xsrfToken = string.Empty;
            string globals = string.Empty;
            string gmail = string.Empty;

            string url = "https://mail.google.com/mail/u/0/?sw=2";
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.CookieContainer = cookies;

            try
            {
                using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
                {
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        string headerValue = response.Headers["X-Gmail-Sw-Cache-Current-Version-Token"];
                        if (!string.IsNullOrEmpty(headerValue) && headerValue.Length >= 19)
                        {
                            versionToken = headerValue.Substring(9, 10);
                        }

                        using (StreamReader reader = new StreamReader(response.GetResponseStream()))
                        {
                            string responseText = reader.ReadToEnd();
                            string pattern1 = @":\[""sdpc"",""([^""]+)""";
                            Match match = Regex.Match(responseText, pattern1);
                            if (match.Success)
                            {
                                xsrfToken = match.Groups[1].Value;
                            }

                            string pattern2 = @"GLOBALS=\[null,null,(\d+),";
                            match = Regex.Match(responseText, pattern2);
                            if (match.Success)
                            {
                                globals = match.Groups[1].Value;
                            }

                            string emailPattern = @"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}";
                            match = Regex.Match(responseText, emailPattern);
                            if (match.Success)
                            {
                                gmail = match.Success ? match.Value : null;
                            }
                        }
                        // 返回包含令牌信息的自定义类实例
                        return new RequestToken
                        {
                            versionToken = versionToken,
                            xsrfToken = xsrfToken,
                            globals = globals,
                            gmail = gmail
                        };
                        //return Tuple.Create(versionToken, xsrfToken, globals, gmail);
                    }
                }
            }
            catch (WebException ex)
            {
                using (StreamReader reader = new StreamReader(ex.Response.GetResponseStream()))
                {
                    string errorText = reader.ReadToEnd();
                    Console.WriteLine("[!] Error: " + errorText);
                }
            }
            return null;
        }
    }
}
